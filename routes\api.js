/**
 * API路由文件
 * 处理所有API请求
 */

const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');

// 导入控制器
const studentController = require('../controllers/studentController');
const typingController = require('../controllers/typingController');
const medalController = require('../controllers/medalController');
const authController = require('../controllers/authController');
const sessionController = require('../controllers/sessionController');
const logController = require('../controllers/logController');
const teacherController = require('../controllers/teacherController');
const teacherRoutes = require('./teacher');
// const schoolController = require('../controllers/schoolController'); // 暂时注释掉，排查500错误
// const articleController = require('../controllers/articleController'); // 暂时注释掉，避免mysql2依赖问题

// JWT认证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ error: '访问令牌是必需的' });
  }

  // 确保使用相同的JWT_SECRET
  const jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key-change-in-production';

  jwt.verify(token, jwtSecret, (err, user) => {
    if (err) {
      // 如果token过期，返回特定错误
      if (err.name === 'TokenExpiredError') {
        return res.status(401).json({ error: '访问令牌已过期，请重新登录' });
      }
      // 其他验证错误
      return res.status(403).json({ error: '无效的访问令牌' });
    }

    req.user = user;
    next();
  });
};

// 测试路由 - 已移动到文件末尾，包含articles测试

// 数据库连接测试路由
router.get('/test-db', async (req, res) => {
  try {
    const db = require('../config/db');

    // 简单的数据库连接测试
    const { data, error } = await db.supabase
      .from('users')
      .select('count', { count: 'exact', head: true });

    if (error) {
      throw error;
    }

    res.json({
      status: 'success',
      message: '数据库连接正常',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('数据库连接测试失败:', error);
    res.status(500).json({
      status: 'error',
      message: '数据库连接失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 注册功能测试路由
router.get('/test-register', (req, res) => {
  res.json({
    message: '注册API端点可用',
    endpoint: '/api/register',
    method: 'POST',
    requiredFields: ['username', 'password', 'confirmPassword'],
    note: '简化版注册，只需要用户名和密码'
  });
});

// 健康检查路由
router.get('/health', async (req, res) => {
  try {
    const db = require('../config/db');

    // 测试数据库连接
    const { data, error } = await db.supabase
      .from('users')
      .select('count', { count: 'exact', head: true });

    if (error) {
      throw error;
    }

    res.json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('健康检查失败:', error);
    res.status(500).json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Supabase前端配置端点
router.get('/supabase-config', (req, res) => {
  try {
    // 检查环境变量
    const supabaseUrl = process.env.SUPABASE_URL;
    const anonKey = process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl) {
      console.error('SUPABASE_URL环境变量未设置');
      return res.status(500).json({
        error: 'Supabase URL未配置'
      });
    }

    // 如果没有anon key，使用service role key（但要注意安全性）
    const clientKey = anonKey || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!clientKey) {
      console.error('Supabase密钥未配置');
      return res.status(500).json({
        error: 'Supabase密钥未配置'
      });
    }

    console.log('返回Supabase配置:', {
      url: supabaseUrl,
      keyType: anonKey ? 'anon' : 'service_role',
      keyPrefix: clientKey.substring(0, 10) + '...'
    });

    // 只返回前端需要的公开配置
    res.json({
      url: supabaseUrl,
      anonKey: clientKey
    });
  } catch (error) {
    console.error('获取Supabase配置失败:', error);
    res.status(500).json({
      error: '无法获取Supabase配置'
    });
  }
});

// 数据库连接测试路由
router.post('/test-db', async (req, res) => {
  try {
    const db = require('../config/db');

    // 测试基本查询
    const { data, error } = await db.supabase
      .from('users')
      .select('id')
      .limit(1);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: '数据库连接正常',
      connection: 'active',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('数据库测试失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      connection: 'failed',
      timestamp: new Date().toISOString()
    });
  }
});

// 检查users表结构
router.get('/check-users-table', async (req, res) => {
  try {
    const db = require('../config/db');

    // 检查表是否存在并获取列信息
    const { data: columns, error } = await db.supabase
      .rpc('get_table_columns', { table_name: 'users' })
      .catch(async () => {
        // 如果RPC不存在，使用备用方法
        return await db.supabase
          .from('information_schema.columns')
          .select('column_name, data_type, is_nullable')
          .eq('table_name', 'users');
      });

    if (error) {
      // 尝试简单查询来检查表是否存在
      const { data: testData, error: testError } = await db.supabase
        .from('users')
        .select('*')
        .limit(0);

      if (testError) {
        throw testError;
      }

      res.json({
        exists: true,
        columns: null,
        message: '表存在但无法获取详细结构信息',
        timestamp: new Date().toISOString()
      });
    } else {
      const hasEmail = columns && columns.some(col =>
        col.column_name === 'email' || col.COLUMN_NAME === 'email'
      );

      res.json({
        exists: true,
        columns: columns,
        hasEmail: hasEmail,
        message: hasEmail ? 'users表结构完整' : 'users表缺少email字段',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('检查users表失败:', error);
    res.status(500).json({
      exists: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 环境变量检查路由
router.get('/check-env', (req, res) => {
  try {
    const envCheck = {
      supabase_url: !!process.env.SUPABASE_URL,
      supabase_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      jwt_secret: !!process.env.JWT_SECRET,
      node_env: process.env.NODE_ENV || 'development',
      port: process.env.PORT || 3000,
      timestamp: new Date().toISOString()
    };

    // 不返回实际的密钥值，只返回是否配置
    res.json(envCheck);
  } catch (error) {
    console.error('环境检查失败:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// ==================== 管理员专用API端点 ====================

// 管理员 - 学生管理
router.get('/admin/students', async (req, res) => {
  try {
    const db = require('../config/db');

    const { data, error } = await db.supabase
      .from('students')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('获取学生数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取学生数据失败',
        error: error.message
      });
    }

    console.log(`成功获取 ${data?.length || 0} 条学生记录`);
    res.json({
      success: true,
      data: data || [],
      count: data?.length || 0
    });

  } catch (error) {
    console.error('学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 教师管理
router.get('/admin/teachers', async (req, res) => {
  try {
    const db = require('../config/db');

    const { data, error } = await db.supabase
      .from('users')
      .select('*')
      .eq('role', 'teacher')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('获取教师数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取教师数据失败',
        error: error.message
      });
    }

    console.log(`成功获取 ${data?.length || 0} 条教师记录`);
    res.json({
      success: true,
      data: data || [],
      count: data?.length || 0
    });

  } catch (error) {
    console.error('教师API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 学校管理
router.get('/admin/schools', async (req, res) => {
  try {
    const db = require('../config/db');

    const { data, error } = await db.supabase
      .from('schools')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('获取学校数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取学校数据失败',
        error: error.message
      });
    }

    console.log(`成功获取 ${data?.length || 0} 条学校记录`);
    res.json({
      success: true,
      data: data || [],
      count: data?.length || 0
    });

  } catch (error) {
    console.error('学校API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 班级管理（基于学生数据生成）
router.get('/admin/classes', async (req, res) => {
  try {
    const db = require('../config/db');

    // 从学生表中获取所有不同的年级和班级组合
    const { data, error } = await db.supabase
      .from('students')
      .select('grade, class, school_id')
      .order('grade', { ascending: true })
      .order('class', { ascending: true });

    if (error) {
      console.error('获取班级数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取班级数据失败',
        error: error.message
      });
    }

    // 去重并统计每个班级的学生数量
    const classMap = new Map();
    data?.forEach(student => {
      const key = `${student.grade}-${student.class}-${student.school_id || 0}`;
      if (classMap.has(key)) {
        classMap.get(key).student_count++;
      } else {
        classMap.set(key, {
          id: key,
          grade: student.grade,
          class: student.class,
          school_id: student.school_id,
          student_count: 1
        });
      }
    });

    const classes = Array.from(classMap.values());

    console.log(`成功获取 ${classes.length} 个班级`);
    res.json({
      success: true,
      data: classes,
      count: classes.length
    });

  } catch (error) {
    console.error('班级API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// ==================== 教师权限管理API端点 ====================

// 管理员 - 获取教师权限
router.get('/admin/teachers/:id/permissions', async (req, res) => {
  try {
    const teacherId = req.params.id;
    const db = require('../config/db');

    // 获取教师的学校分配
    const { data: schoolAssignments, error: schoolError } = await db.supabase
      .from('teacher_school_assignments')
      .select(`
        *,
        schools(
          id,
          name
        )
      `)
      .eq('teacher_id', teacherId);

    if (schoolError) {
      console.error('获取教师学校分配失败:', schoolError);
      return res.status(500).json({
        success: false,
        message: '获取教师学校分配失败',
        error: schoolError.message
      });
    }

    // 获取教师的班级权限
    const { data: classPermissions, error: classError } = await db.supabase
      .from('teacher_class_permissions')
      .select(`
        *,
        schools(
          id,
          name
        )
      `)
      .eq('teacher_id', teacherId)
      .order('school_id')
      .order('grade')
      .order('class');

    if (classError) {
      console.error('获取教师班级权限失败:', classError);
      return res.status(500).json({
        success: false,
        message: '获取教师班级权限失败',
        error: classError.message
      });
    }

    res.json({
      success: true,
      data: {
        schools: schoolAssignments || [],
        classes: classPermissions || []
      }
    });

  } catch (error) {
    console.error('获取教师权限失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 设置教师权限
router.post('/admin/teachers/:id/permissions', async (req, res) => {
  try {
    const teacherId = req.params.id;
    const { schools, classes } = req.body;
    const db = require('../config/db');

    // 开始事务
    // 首先清除现有权限
    await db.supabase
      .from('teacher_class_permissions')
      .delete()
      .eq('teacher_id', teacherId);

    await db.supabase
      .from('teacher_school_assignments')
      .delete()
      .eq('teacher_id', teacherId);

    // 添加学校分配
    if (schools && schools.length > 0) {
      const schoolAssignments = schools.map(schoolId => ({
        teacher_id: parseInt(teacherId),
        school_id: parseInt(schoolId)
      }));

      const { error: schoolError } = await db.supabase
        .from('teacher_school_assignments')
        .insert(schoolAssignments);

      if (schoolError) {
        console.error('添加教师学校分配失败:', schoolError);
        return res.status(500).json({
          success: false,
          message: '添加教师学校分配失败',
          error: schoolError.message
        });
      }
    }

    // 添加班级权限
    if (classes && classes.length > 0) {
      const classPermissions = classes.map(cls => ({
        teacher_id: parseInt(teacherId),
        school_id: parseInt(cls.school_id),
        grade: parseInt(cls.grade),
        class: parseInt(cls.class)
      }));

      const { error: classError } = await db.supabase
        .from('teacher_class_permissions')
        .insert(classPermissions);

      if (classError) {
        console.error('添加教师班级权限失败:', classError);
        return res.status(500).json({
          success: false,
          message: '添加教师班级权限失败',
          error: classError.message
        });
      }
    }

    res.json({
      success: true,
      message: '教师权限设置成功'
    });

  } catch (error) {
    console.error('设置教师权限失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 获取学校的年级班级配置
router.get('/admin/schools/:id/config', async (req, res) => {
  try {
    const schoolId = req.params.id;
    const db = require('../config/db');

    const { data, error } = await db.supabase
      .from('school_grade_configs')
      .select('*')
      .eq('school_id', schoolId)
      .order('grade');

    if (error) {
      console.error('获取学校配置失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取学校配置失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('获取学校配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 更新学校的年级班级配置
router.put('/admin/schools/:id/config', async (req, res) => {
  try {
    const schoolId = req.params.id;
    const { grades } = req.body; // grades: [{grade: 1, class_count: 3}, ...]
    const db = require('../config/db');

    // 删除现有配置
    await db.supabase
      .from('school_grade_configs')
      .delete()
      .eq('school_id', schoolId);

    // 添加新配置
    if (grades && grades.length > 0) {
      const configs = grades.map(g => ({
        school_id: parseInt(schoolId),
        grade: parseInt(g.grade),
        class_count: parseInt(g.class_count)
      }));

      const { error } = await db.supabase
        .from('school_grade_configs')
        .insert(configs);

      if (error) {
        console.error('更新学校配置失败:', error);
        return res.status(500).json({
          success: false,
          message: '更新学校配置失败',
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      message: '学校配置更新成功'
    });

  } catch (error) {
    console.error('更新学校配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 添加教师
router.post('/admin/teachers', async (req, res) => {
  try {
    const { username, password, display_name, email, schools, classes } = req.body;

    if (!username || !password || !display_name) {
      return res.status(400).json({
        success: false,
        message: '用户名、密码和显示名称为必填项'
      });
    }

    const db = require('../config/db');
    const bcrypt = require('bcrypt');

    // 检查用户名是否已存在
    const { data: existingUser } = await db.supabase
      .from('users')
      .select('username')
      .eq('username', username)
      .single();

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在'
      });
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);

    // 插入新教师
    const { data, error } = await db.supabase
      .from('users')
      .insert([{
        username,
        password: hashedPassword,
        display_name,
        email: email || null,
        role: 'teacher'
      }])
      .select()
      .single();

    if (error) {
      console.error('添加教师失败:', error);
      return res.status(500).json({
        success: false,
        message: '添加教师失败',
        error: error.message
      });
    }

    // 如果提供了学校和班级权限信息，则设置权限
    if (schools && schools.length > 0) {
      const teacherId = data.id;

      // 添加学校分配
      const schoolAssignments = schools.map(schoolId => ({
        teacher_id: teacherId,
        school_id: parseInt(schoolId)
      }));

      const { error: schoolError } = await db.supabase
        .from('teacher_school_assignments')
        .insert(schoolAssignments);

      if (schoolError) {
        console.error('添加教师学校分配失败:', schoolError);
        // 继续执行，但记录错误
      }

      // 添加班级权限
      if (classes && classes.length > 0) {
        const classPermissions = classes.map(cls => ({
          teacher_id: teacherId,
          school_id: parseInt(cls.school_id),
          grade: parseInt(cls.grade),
          class: parseInt(cls.class)
        }));

        const { error: classError } = await db.supabase
          .from('teacher_class_permissions')
          .insert(classPermissions);

        if (classError) {
          console.error('添加教师班级权限失败:', classError);
          // 继续执行，但记录错误
        }
      }
    }

    console.log('成功添加教师:', data);
    res.json({
      success: true,
      message: '教师添加成功',
      data: {
        id: data.id,
        username: data.username,
        display_name: data.display_name,
        email: data.email,
        role: data.role,
        created_at: data.created_at
      }
    });

  } catch (error) {
    console.error('添加教师API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 更新教师信息
router.put('/admin/teachers/:id', async (req, res) => {
  try {
    const teacherId = req.params.id;
    const { display_name, email, new_password } = req.body;

    const db = require('../config/db');
    const updateData = {};

    if (display_name) updateData.display_name = display_name;
    if (email !== undefined) updateData.email = email;

    // 如果提供了新密码，加密后更新
    if (new_password) {
      const bcrypt = require('bcrypt');
      updateData.password = await bcrypt.hash(new_password, 10);
    }

    updateData.updated_at = new Date().toISOString();

    const { data, error } = await db.supabase
      .from('users')
      .update(updateData)
      .eq('id', teacherId)
      .eq('role', 'teacher')
      .select()
      .single();

    if (error) {
      console.error('更新教师失败:', error);
      return res.status(500).json({
        success: false,
        message: '更新教师失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '教师不存在'
      });
    }

    console.log('成功更新教师:', data);
    res.json({
      success: true,
      message: '教师信息更新成功',
      data: {
        id: data.id,
        username: data.username,
        display_name: data.display_name,
        email: data.email,
        role: data.role,
        updated_at: data.updated_at
      }
    });

  } catch (error) {
    console.error('更新教师API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 删除教师
router.delete('/admin/teachers/:id', async (req, res) => {
  try {
    const teacherId = req.params.id;
    const db = require('../config/db');

    const { data, error } = await db.supabase
      .from('users')
      .delete()
      .eq('id', teacherId)
      .eq('role', 'teacher')
      .select()
      .single();

    if (error) {
      console.error('删除教师失败:', error);
      return res.status(500).json({
        success: false,
        message: '删除教师失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '教师不存在'
      });
    }

    console.log('成功删除教师:', data);
    res.json({
      success: true,
      message: '教师删除成功'
    });

  } catch (error) {
    console.error('删除教师API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 添加学校
router.post('/admin/schools', async (req, res) => {
  try {
    const { name, address, contact_phone, contact_email, description } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '学校名称为必填项'
      });
    }

    const db = require('../config/db');

    // 检查学校名称是否已存在
    const { data: existingSchool } = await db.supabase
      .from('schools')
      .select('name')
      .eq('name', name)
      .single();

    if (existingSchool) {
      return res.status(400).json({
        success: false,
        message: '学校名称已存在'
      });
    }

    // 插入新学校
    const { data, error } = await db.supabase
      .from('schools')
      .insert([{
        name,
        address: address || null,
        contact_phone: contact_phone || null,
        contact_email: contact_email || null,
        description: description || null
      }])
      .select()
      .single();

    if (error) {
      console.error('添加学校失败:', error);
      return res.status(500).json({
        success: false,
        message: '添加学校失败',
        error: error.message
      });
    }

    console.log('成功添加学校:', data);
    res.json({
      success: true,
      message: '学校添加成功',
      data: data
    });

  } catch (error) {
    console.error('添加学校API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 更新学校信息
router.put('/admin/schools/:id', async (req, res) => {
  try {
    const schoolId = req.params.id;
    const { name, address, contact_phone, contact_email, description } = req.body;

    const db = require('../config/db');
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (name) updateData.name = name;
    if (address !== undefined) updateData.address = address;
    if (contact_phone !== undefined) updateData.contact_phone = contact_phone;
    if (contact_email !== undefined) updateData.contact_email = contact_email;
    if (description !== undefined) updateData.description = description;

    const { data, error } = await db.supabase
      .from('schools')
      .update(updateData)
      .eq('id', schoolId)
      .select()
      .single();

    if (error) {
      console.error('更新学校失败:', error);
      return res.status(500).json({
        success: false,
        message: '更新学校失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '学校不存在'
      });
    }

    console.log('成功更新学校:', data);
    res.json({
      success: true,
      message: '学校信息更新成功',
      data: data
    });

  } catch (error) {
    console.error('更新学校API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 删除学校
router.delete('/admin/schools/:id', async (req, res) => {
  try {
    const schoolId = req.params.id;
    const db = require('../config/db');

    // 检查是否有学生关联到这个学校
    const { data: students } = await db.supabase
      .from('students')
      .select('id')
      .eq('school_id', schoolId)
      .limit(1);

    if (students && students.length > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除学校，还有学生关联到此学校'
      });
    }

    const { data, error } = await db.supabase
      .from('schools')
      .delete()
      .eq('id', schoolId)
      .select()
      .single();

    if (error) {
      console.error('删除学校失败:', error);
      return res.status(500).json({
        success: false,
        message: '删除学校失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '学校不存在'
      });
    }

    console.log('成功删除学校:', data);
    res.json({
      success: true,
      message: '学校删除成功'
    });

  } catch (error) {
    console.error('删除学校API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 打字记录相关路由
router.get('/typing-records', async (req, res) => {
  try {
    const { student_identifier } = req.query;
    console.log('查询打字记录，学生标识符:', student_identifier);

    if (!student_identifier) {
      return res.status(400).json({
        success: false,
        error: '学生标识符为必填项'
      });
    }

    const db = require('../config/db');

    // 获取打字记录
    const { data: records, error } = await db.supabase
      .from('typing_records')
      .select('*')
      .eq('student_identifier', student_identifier)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('查询打字记录失败:', error);
      return res.status(500).json({
        success: false,
        error: '查询打字记录失败: ' + error.message
      });
    }

    console.log(`找到${records?.length || 0}条打字记录`);

    res.json({
      success: true,
      records: records || [],
      count: records ? records.length : 0
    });

  } catch (error) {
    console.error('查询打字记录异常:', error);
    res.status(500).json({
      success: false,
      error: '服务器错误: ' + error.message
    });
  }
});
router.post('/typing-records', async (req, res) => {
  console.log('=== 开始处理打字记录 ===');
  console.log('请求数据:', req.body);

  try {
    // 先返回成功，测试路由是否正常
    console.log('路由正常，准备处理数据库操作');

    const { student_identifier, speed, accuracy } = req.body;

    console.log('解析的数据:', {
      student_identifier,
      speed,
      accuracy,
      speedType: typeof speed,
      accuracyType: typeof accuracy
    });

    // 检查数据库连接
    const db = require('../config/db');
    console.log('数据库对象:', !!db);
    console.log('Supabase对象:', !!db?.supabase);

    if (!db || !db.supabase) {
      console.error('数据库连接失败');
      return res.status(500).json({ error: '数据库连接失败' });
    }

    // 准备插入数据
    const insertData = {
      student_identifier: String(student_identifier),
      speed: Number(speed),
      accuracy: Number(accuracy) || 100
    };

    console.log('准备插入的数据:', insertData);
    console.log('数据类型检查:', {
      student_identifier: typeof insertData.student_identifier,
      speed: typeof insertData.speed,
      accuracy: typeof insertData.accuracy
    });

    // 先检查学生是否存在
    console.log('检查学生是否存在:', insertData.student_identifier);
    const { data: existingStudent, error: studentError } = await db.supabase
      .from('students')
      .select('student_identifier')
      .eq('student_identifier', insertData.student_identifier)
      .single();

    console.log('学生查询结果:', { existingStudent, studentError });

    if (studentError && studentError.code !== 'PGRST116') {
      // PGRST116 是"没有找到记录"的错误码，其他错误需要处理
      console.error('查询学生时出错:', studentError);
      return res.status(500).json({
        error: '查询学生信息失败',
        details: studentError.message
      });
    }

    if (!existingStudent) {
      console.log('学生不存在，跳过保存或返回错误');
      return res.status(400).json({
        error: '学生不存在',
        message: `学生标识符 "${insertData.student_identifier}" 在数据库中不存在`,
        suggestion: '请检查学生是否已正确签到'
      });
    }

    console.log('学生存在，继续保存打字记录');

    // 执行插入
    console.log('开始执行数据库插入...');
    const result = await db.supabase
      .from('typing_records')
      .insert(insertData);

    console.log('数据库操作完成');
    console.log('插入结果:', result);
    console.log('错误信息:', result.error);
    console.log('返回数据:', result.data);

    if (result.error) {
      console.error('数据库插入错误:', result.error);
      console.error('错误详情:', JSON.stringify(result.error, null, 2));
      return res.status(500).json({
        error: '数据库插入失败',
        details: result.error.message,
        code: result.error.code
      });
    }

    console.log('=== 打字记录保存成功 ===');
    res.json({
      success: true,
      message: '保存成功'
    });

  } catch (error) {
    console.error('=== 路由异常 ===');
    console.error('异常类型:', error.name);
    console.error('异常消息:', error.message);
    console.error('异常堆栈:', error.stack);

    res.status(500).json({
      error: '服务器异常',
      message: error.message,
      type: error.name
    });
  }
});
router.post('/batch-update-typing', typingController.batchUpdateTypingSpeed);
router.get('/best-typing-records', authenticateToken, typingController.getBestTypingRecords);







// 管理员 - 添加学生
router.post('/admin/students', async (req, res) => {
  try {
    const { student_identifier, name, grade, class: studentClass, school_id } = req.body;

    if (!student_identifier || !name || !grade || !studentClass) {
      return res.status(400).json({
        success: false,
        message: '学生标识符、姓名、年级和班级为必填项'
      });
    }

    const db = require('../config/db');

    // 检查学生标识符是否已存在
    const { data: existingStudent } = await db.supabase
      .from('students')
      .select('student_identifier')
      .eq('student_identifier', student_identifier)
      .single();

    if (existingStudent) {
      return res.status(400).json({
        success: false,
        message: '学生标识符已存在'
      });
    }

    // 插入新学生
    const { data, error } = await db.supabase
      .from('students')
      .insert([{
        student_identifier,
        name,
        grade: parseInt(grade),
        class: parseInt(studentClass),
        school_id: school_id ? parseInt(school_id) : null
      }])
      .select()
      .single();

    if (error) {
      console.error('添加学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '添加学生失败',
        error: error.message
      });
    }

    console.log('成功添加学生:', data);
    res.json({
      success: true,
      message: '学生添加成功',
      data: data
    });

  } catch (error) {
    console.error('添加学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 更新学生信息
router.put('/admin/students/:id', async (req, res) => {
  try {
    const studentId = req.params.id;
    const { name, grade, class: studentClass, school_id } = req.body;

    const db = require('../config/db');
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (name) updateData.name = name;
    if (grade) updateData.grade = parseInt(grade);
    if (studentClass) updateData.class = parseInt(studentClass);
    if (school_id !== undefined) updateData.school_id = school_id ? parseInt(school_id) : null;

    const { data, error } = await db.supabase
      .from('students')
      .update(updateData)
      .eq('id', studentId)
      .select()
      .single();

    if (error) {
      console.error('更新学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '更新学生失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '学生不存在'
      });
    }

    console.log('成功更新学生:', data);
    res.json({
      success: true,
      message: '学生信息更新成功',
      data: data
    });

  } catch (error) {
    console.error('更新学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 管理员 - 删除学生
router.delete('/admin/students/:id', async (req, res) => {
  try {
    const studentId = req.params.id;
    const db = require('../config/db');

    const { data, error } = await db.supabase
      .from('students')
      .delete()
      .eq('id', studentId)
      .select()
      .single();

    if (error) {
      console.error('删除学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '删除学生失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '学生不存在'
      });
    }

    console.log('成功删除学生:', data);
    res.json({
      success: true,
      message: '学生删除成功'
    });

  } catch (error) {
    console.error('删除学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 数据库诊断端点（调试用）
router.get('/db-diagnostic', authenticateToken, async (req, res) => {
  try {
    const db = require('../config/db');
    const diagnostic = {
      timestamp: new Date().toISOString(),
      user: req.user,
      tables: {}
    };

    // 检查学生表
    try {
      const { data: students, error: studentsError } = await db.supabase
        .from('students')
        .select('*')
        .limit(3);
      diagnostic.tables.students = {
        exists: !studentsError,
        count: students?.length || 0,
        sample: students?.[0] || null,
        error: studentsError?.message || null
      };
    } catch (err) {
      diagnostic.tables.students = { exists: false, error: err.message };
    }

    // 检查奖章表
    try {
      const { data: medals, error: medalsError } = await db.supabase
        .from('medals')
        .select('*')
        .limit(3);
      diagnostic.tables.medals = {
        exists: !medalsError,
        count: medals?.length || 0,
        sample: medals?.[0] || null,
        error: medalsError?.message || null
      };
    } catch (err) {
      diagnostic.tables.medals = { exists: false, error: err.message };
    }

    // 检查学校表
    try {
      const { data: schools, error: schoolsError } = await db.supabase
        .from('schools')
        .select('*')
        .limit(3);
      diagnostic.tables.schools = {
        exists: !schoolsError,
        count: schools?.length || 0,
        sample: schools?.[0] || null,
        error: schoolsError?.message || null
      };
    } catch (err) {
      diagnostic.tables.schools = { exists: false, error: err.message };
    }

    // 检查用户表
    try {
      const { data: users, error: usersError } = await db.supabase
        .from('users')
        .select('id, username, role, display_name')
        .limit(3);
      diagnostic.tables.users = {
        exists: !usersError,
        count: users?.length || 0,
        sample: users?.[0] || null,
        error: usersError?.message || null
      };
    } catch (err) {
      diagnostic.tables.users = { exists: false, error: err.message };
    }

    // 检查教师权限表
    try {
      const { data: permissions, error: permError } = await db.supabase
        .from('teacher_class_permissions')
        .select('*')
        .eq('teacher_id', req.user.id)
        .limit(5);
      diagnostic.tables.teacher_permissions = {
        exists: !permError,
        count: permissions?.length || 0,
        sample: permissions?.[0] || null,
        error: permError?.message || null,
        user_permissions: permissions || []
      };
    } catch (err) {
      diagnostic.tables.teacher_permissions = { exists: false, error: err.message };
    }

    // 检查学生和奖章的关联查询
    try {
      const { data: studentsWithMedals, error: joinError } = await db.supabase
        .from('students')
        .select(`
          *,
          medals(count),
          schools(name)
        `)
        .limit(3);
      diagnostic.tables.students_with_medals = {
        exists: !joinError,
        count: studentsWithMedals?.length || 0,
        sample: studentsWithMedals?.[0] || null,
        error: joinError?.message || null
      };
    } catch (err) {
      diagnostic.tables.students_with_medals = { exists: false, error: err.message };
    }

    res.json({ success: true, diagnostic });
  } catch (err) {
    console.error('诊断端点错误:', err);
    res.status(500).json({ error: '诊断失败', details: err.message });
  }
});

// 奖章相关路由（需要认证）
router.get('/medals', authenticateToken, medalController.getMedals);
router.post('/medals', authenticateToken, medalController.updateMedal);
router.post('/batch-update-medals', authenticateToken, medalController.batchUpdateMedals);

// 认证相关路由
router.post('/login', authController.login);
router.post('/register', authController.register);

// Token验证路由
router.get('/auth/validate', authenticateToken, (req, res) => {
  res.json({
    success: true,
    user: req.user,
    message: '用户认证有效'
  });
});

// ==================== 教师专用API端点 ====================

// 教师 - 获取任教学校
router.get('/teacher/schools', authenticateToken, async (req, res) => {
  try {
    const db = require('../config/db');

    // 如果是管理员，获取所有学校
    if (req.user.role === 'admin') {
      const { data, error } = await db.supabase
        .from('schools')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('获取学校数据失败:', error);
        return res.status(500).json({
          success: false,
          message: '获取学校数据失败',
          error: error.message
        });
      }

      return res.json(data || []);
    }

    // 教师只能看到关联的学校
    const { data, error } = await db.supabase
      .from('schools')
      .select(`
        *,
        teacher_school_assignments!inner(
          teacher_id
        )
      `)
      .eq('teacher_school_assignments.teacher_id', req.user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('获取教师学校数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取教师学校数据失败',
        error: error.message
      });
    }

    res.json(data || []);

  } catch (error) {
    console.error('获取教师学校失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 获取任教学生
router.get('/teacher/students', authenticateToken, async (req, res) => {
  try {
    const db = require('../config/db');

    // 如果是管理员，获取所有学生
    if (req.user.role === 'admin') {
      const { data, error } = await db.supabase
        .from('students')
        .select(`
          *,
          schools(
            id,
            name
          )
        `)
        .order('grade', { ascending: true })
        .order('class', { ascending: true })
        .order('name', { ascending: true });

      if (error) {
        console.error('获取学生数据失败:', error);
        return res.status(500).json({
          success: false,
          message: '获取学生数据失败',
          error: error.message
        });
      }

      return res.json(data || []);
    }

    // 教师只能看到自己任教班级的学生
    // 首先获取教师有权限的学校ID列表
    const { data: schoolAssignments, error: schoolError } = await db.supabase
      .from('teacher_school_assignments')
      .select('school_id')
      .eq('teacher_id', req.user.id);

    if (schoolError) {
      console.error('获取教师学校权限失败:', schoolError);
      return res.status(500).json({
        success: false,
        message: '获取教师学校权限失败',
        error: schoolError.message
      });
    }

    // 提取学校ID数组
    const schoolIds = schoolAssignments.map(assignment => assignment.school_id);

    if (schoolIds.length === 0) {
      // 教师没有分配任何学校，返回空数组
      return res.json({
        success: true,
        data: []
      });
    }

    // 使用学校ID数组查询学生
    const { data, error } = await db.supabase
      .from('students')
      .select(`
        *,
        schools(
          id,
          name
        )
      `)
      .in('school_id', schoolIds)
      .order('grade', { ascending: true })
      .order('class', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('获取教师学生数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取教师学生数据失败',
        error: error.message
      });
    }

    // 进一步过滤：只返回教师有权限管理的班级的学生
    if (data && data.length > 0) {
      // 获取教师的班级权限
      const { data: permissions } = await db.supabase
        .from('teacher_class_permissions')
        .select('school_id, grade, class')
        .eq('teacher_id', req.user.id);

      if (permissions && permissions.length > 0) {
        // 创建权限映射
        const permissionMap = new Set();
        permissions.forEach(p => {
          permissionMap.add(`${p.school_id}-${p.grade}-${p.class}`);
        });

        // 过滤学生
        const filteredStudents = data.filter(student => {
          return permissionMap.has(`${student.school_id}-${student.grade}-${student.class}`);
        });

        return res.json(filteredStudents);
      }
    }

    res.json(data || []);

  } catch (error) {
    console.error('获取教师学生失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 获取学校年级配置
router.get('/teacher/schools/:id/grades', authenticateToken, async (req, res) => {
  try {
    const schoolId = req.params.id;
    const db = require('../config/db');

    // 检查教师是否有权限访问该学校（优化查询）
    if (req.user.role !== 'admin') {
      const { data: assignment, error: permError } = await db.supabase
        .from('teacher_school_assignments')
        .select('id')
        .eq('teacher_id', req.user.id)
        .eq('school_id', schoolId)
        .maybeSingle(); // 使用maybeSingle避免没有数据时的错误

      if (permError) {
        console.error('权限检查失败:', permError);
        return res.status(500).json({
          success: false,
          message: '权限检查失败'
        });
      }

      if (!assignment) {
        return res.status(403).json({
          success: false,
          message: '您没有权限访问该学校的配置'
        });
      }
    }

    const { data, error } = await db.supabase
      .from('school_grade_configs')
      .select('*')
      .eq('school_id', schoolId)
      .order('grade');

    if (error) {
      console.error('获取学校年级配置失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取学校年级配置失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('获取学校年级配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 保存单个年级配置
router.post('/teacher/schools/:id/grades/:grade', authenticateToken, async (req, res) => {
  try {
    const schoolId = req.params.id;
    const grade = req.params.grade;
    const { class_count } = req.body;

    if (!class_count || class_count < 1) {
      return res.status(400).json({
        success: false,
        message: '班级数量必须大于0'
      });
    }

    const db = require('../config/db');

    // 检查教师是否有权限访问该学校
    if (req.user.role !== 'admin') {
      const { data: assignment } = await db.supabase
        .from('teacher_school_assignments')
        .select('id')
        .eq('teacher_id', req.user.id)
        .eq('school_id', schoolId)
        .single();

      if (!assignment) {
        return res.status(403).json({
          success: false,
          message: '您没有权限配置该学校的年级'
        });
      }
    }

    // 删除现有配置
    await db.supabase
      .from('school_grade_configs')
      .delete()
      .eq('school_id', schoolId)
      .eq('grade', grade);

    // 插入新配置
    const { data, error } = await db.supabase
      .from('school_grade_configs')
      .insert([{
        school_id: parseInt(schoolId),
        grade: parseInt(grade),
        class_count: parseInt(class_count)
      }])
      .select()
      .single();

    if (error) {
      console.error('保存年级配置失败:', error);
      return res.status(500).json({
        success: false,
        message: '保存年级配置失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: `${grade}年级配置保存成功`,
      data: data
    });

  } catch (error) {
    console.error('保存年级配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 获取任教班级
router.get('/teacher/classes', authenticateToken, async (req, res) => {
  try {
    const db = require('../config/db');

    // 如果是管理员，获取所有班级
    if (req.user.role === 'admin') {
      const { data, error } = await db.supabase
        .from('students')
        .select(`
          grade,
          class,
          school_id,
          schools(
            id,
            name
          )
        `)
        .order('grade', { ascending: true })
        .order('class', { ascending: true });

      if (error) {
        console.error('获取班级数据失败:', error);
        return res.status(500).json({
          success: false,
          message: '获取班级数据失败',
          error: error.message
        });
      }

      // 统计每个班级的学生数量
      const classMap = new Map();
      data?.forEach(student => {
        const key = `${student.grade}-${student.class}-${student.school_id || 0}`;
        if (classMap.has(key)) {
          classMap.get(key).student_count++;
        } else {
          classMap.set(key, {
            id: key,
            grade: student.grade,
            class: student.class,
            school_id: student.school_id,
            school_name: student.schools?.name || '未知学校',
            student_count: 1
          });
        }
      });

      const classes = Array.from(classMap.values());
      return res.json(classes || []);
    }

    // 教师只能看到有权限的班级
    const { data: permissions, error } = await db.supabase
      .from('teacher_class_permissions')
      .select(`
        *,
        schools(
          id,
          name
        )
      `)
      .eq('teacher_id', req.user.id)
      .order('grade', { ascending: true })
      .order('class', { ascending: true });

    if (error) {
      console.error('获取教师班级权限失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取教师班级权限失败',
        error: error.message
      });
    }

    // 为每个有权限的班级统计学生数量
    const classes = [];
    for (const permission of permissions || []) {
      const { data: students } = await db.supabase
        .from('students')
        .select('id')
        .eq('school_id', permission.school_id)
        .eq('grade', permission.grade)
        .eq('class', permission.class);

      classes.push({
        id: `${permission.grade}-${permission.class}-${permission.school_id}`,
        grade: permission.grade,
        class: permission.class,
        school_id: permission.school_id,
        school_name: permission.schools?.name || '未知学校',
        student_count: students?.length || 0
      });
    }

    res.json(classes);

  } catch (error) {
    console.error('获取教师班级失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 获取当前教师的班级权限（可按学校过滤）
router.get('/teacher/classes/permissions', authenticateToken, async (req, res) => {
  try {
    const db = require('../config/db');
    const schoolId = req.query.school_id;

    let query = db.supabase
      .from('teacher_class_permissions')
      .select('school_id, grade, class')
      .eq('teacher_id', req.user.id)
      .order('school_id')
      .order('grade')
      .order('class');

    if (schoolId) {
      query = query.eq('school_id', schoolId);
    }

    const { data, error } = await query;
    if (error) {
      console.error('获取教师班级权限失败:', error);
      return res.status(500).json({ success: false, message: '获取教师班级权限失败', error: error.message });
    }

    res.json({ success: true, data: data || [] });
  } catch (error) {
    console.error('教师班级权限API错误:', error);
    res.status(500).json({ success: false, message: '服务器内部错误', error: error.message });
  }
});

// 教师 - 设置当前教师的班级权限（全量覆盖；可按学校覆盖）
router.post('/teacher/classes/permissions', authenticateToken, async (req, res) => {
  try {
    const db = require('../config/db');
    const { classes, school_id } = req.body || {};

    // 先删除当前教师的权限（可选按学校过滤）
    let del = db.supabase
      .from('teacher_class_permissions')
      .delete()
      .eq('teacher_id', req.user.id);
    if (school_id) {
      del = del.eq('school_id', parseInt(school_id));
    }
    const { error: delError } = await del;
    if (delError) {
      console.error('清除教师班级权限失败:', delError);
      return res.status(500).json({ success: false, message: '清除教师班级权限失败', error: delError.message });
    }

    if (Array.isArray(classes) && classes.length > 0) {
      const rows = classes.map(c => ({
        teacher_id: req.user.id,
        school_id: parseInt(c.school_id),
        grade: parseInt(c.grade),
        class: parseInt(c.class)
      }));

      const { error: insError } = await db.supabase
        .from('teacher_class_permissions')
        .insert(rows);
      if (insError) {
        console.error('写入教师班级权限失败:', insError);
        return res.status(500).json({ success: false, message: '写入教师班级权限失败', error: insError.message });
      }
    }

    res.json({ success: true, message: '任教班级设置已更新' });
  } catch (error) {
    console.error('设置教师班级权限API错误:', error);
    res.status(500).json({ success: false, message: '服务器内部错误', error: error.message });
  }
});


// 教师 - 添加学校
router.post('/teacher/schools', authenticateToken, async (req, res) => {
  try {
    const { name, address, phone } = req.body;

    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: '学校名称不能为空'
      });
    }

    const db = require('../config/db');

    // 检查学校名称是否已存在
    const { data: existingSchool } = await db.supabase
      .from('schools')
      .select('name')
      .eq('name', name.trim())
      .single();

    if (existingSchool) {
      return res.status(400).json({
        success: false,
        message: '学校名称已存在'
      });
    }

    // 插入新学校
    const { data: schoolData, error: schoolError } = await db.supabase
      .from('schools')
      .insert([{
        name: name.trim(),
        address: address?.trim() || null,
        contact_phone: phone?.trim() || null
      }])
      .select()
      .single();

    if (schoolError) {
      console.error('添加学校失败:', schoolError);
      return res.status(500).json({
        success: false,
        message: '添加学校失败',
        error: schoolError.message
      });
    }

    // 为当前教师创建学校关联（非管理员用户）
    if (req.user.role !== 'admin') {
      console.log(`为教师 ${req.user.id} (${req.user.username}) 创建学校 ${schoolData.id} (${schoolData.name}) 的关联`);

      const { data: assignmentData, error: assignmentError } = await db.supabase
        .from('teacher_school_assignments')
        .insert([{
          teacher_id: req.user.id,
          school_id: schoolData.id
        }])
        .select();

      if (assignmentError) {
        console.error('创建教师学校关联失败:', assignmentError);
        console.error('关联数据:', { teacher_id: req.user.id, school_id: schoolData.id });
        // 注意：即使关联创建失败，学校已经创建成功，所以返回警告而不是错误
        return res.json({
          success: true,
          message: '学校添加成功，但权限分配失败',
          warning: '您可能需要联系管理员分配学校权限',
          data: schoolData
        });
      }

      console.log('教师学校关联创建成功:', assignmentData);
    } else {
      console.log('管理员用户，跳过创建教师学校关联');
    }

    res.json({
      success: true,
      message: '学校添加成功',
      data: schoolData
    });

  } catch (error) {
    console.error('添加学校API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 更新学校信息
router.put('/teacher/schools/:id', authenticateToken, async (req, res) => {
  try {
    const schoolId = req.params.id;
    const { name, address, phone } = req.body;

    const db = require('../config/db');
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (name) updateData.name = name.trim();
    if (address !== undefined) updateData.address = address?.trim() || null;
    if (phone !== undefined) updateData.contact_phone = phone?.trim() || null;

    const { data, error } = await db.supabase
      .from('schools')
      .update(updateData)
      .eq('id', schoolId)
      .select()
      .single();

    if (error) {
      console.error('更新学校失败:', error);
      return res.status(500).json({
        success: false,
        message: '更新学校失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '学校不存在'
      });
    }

    res.json({
      success: true,
      message: '学校信息更新成功',
      data: data
    });

  } catch (error) {
    console.error('更新学校API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 删除学校
router.delete('/teacher/schools/:id', authenticateToken, async (req, res) => {
  try {
    const schoolId = req.params.id;
    const db = require('../config/db');

    // 检查是否有学生关联到这个学校
    const { data: students } = await db.supabase
      .from('students')
      .select('id')
      .eq('school_id', schoolId)
      .limit(1);

    if (students && students.length > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除学校，还有学生关联到此学校'
      });
    }

    const { data, error } = await db.supabase
      .from('schools')
      .delete()
      .eq('id', schoolId)
      .select()
      .single();

    if (error) {
      console.error('删除学校失败:', error);
      return res.status(500).json({
        success: false,
        message: '删除学校失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '学校不存在'
      });
    }

    res.json({
      success: true,
      message: '学校删除成功'
    });

  } catch (error) {
    console.error('删除学校API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 添加学生
router.post('/teacher/students', authenticateToken, async (req, res) => {
  try {
    const { name, student_id, school_id, grade, class: studentClass, group_number, gender } = req.body;

    if (!name || !name.trim() || !grade || !studentClass) {
      return res.status(400).json({
        success: false,
        message: '学生姓名、年级和班级为必填项'
      });
    }

    const db = require('../config/db');

    // 生成学生标识符（如果没有提供student_id）
    let studentIdentifier = student_id?.trim();
    if (!studentIdentifier) {
      // 自动生成学号：年级+班级+时间戳后4位
      const timestamp = Date.now().toString().slice(-4);
      studentIdentifier = `${grade}${String(studentClass).padStart(2, '0')}${timestamp}`;
    }

    // 检查学生标识符是否已存在
    const { data: existingStudent } = await db.supabase
      .from('students')
      .select('student_identifier')
      .eq('student_identifier', studentIdentifier)
      .single();

    if (existingStudent) {
      return res.status(400).json({
        success: false,
        message: '学号已存在，请使用其他学号'
      });
    }

    // 准备插入数据
    const insertData = {
      student_identifier: studentIdentifier,
      name: name.trim(),
      grade: parseInt(grade),
      class: parseInt(studentClass),
      school_id: school_id ? parseInt(school_id) : null
    };

    // 添加可选字段
    if (group_number && !isNaN(parseInt(group_number))) {
      insertData.group_number = parseInt(group_number);
    }

    if (gender && gender.trim()) {
      insertData.gender = gender.trim();
    }

    // 插入新学生
    const { data, error } = await db.supabase
      .from('students')
      .insert([insertData])
      .select()
      .single();

    if (error) {
      console.error('添加学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '添加学生失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: '学生添加成功',
      data: data
    });

  } catch (error) {
    console.error('添加学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 更新学生信息
router.put('/teacher/students/:id', authenticateToken, async (req, res) => {
  try {
    const studentId = req.params.id;
    const { name, student_id, grade, class: studentClass, gender, group_number } = req.body;

    const db = require('../config/db');
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (name) updateData.name = name.trim();
    if (student_id !== undefined) updateData.student_identifier = student_id.trim();
    if (grade) updateData.grade = parseInt(grade);
    if (studentClass) updateData.class = parseInt(studentClass);
    if (gender !== undefined) updateData.gender = gender ? String(gender).trim() : null;
    if (group_number !== undefined) updateData.group_number = group_number === null || group_number === '' ? null : parseInt(group_number);

    const { data, error } = await db.supabase
      .from('students')
      .update(updateData)
      .eq('id', studentId)
      .select()
      .single();

    if (error) {
      console.error('更新学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '更新学生失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '学生不存在'
      });
    }

    res.json({
      success: true,
      message: '学生信息更新成功',
      data: data
    });

  } catch (error) {
    console.error('更新学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 教师 - 删除学生
router.delete('/teacher/students/:id', authenticateToken, async (req, res) => {
  try {
    const studentId = req.params.id;
    const db = require('../config/db');

    const { data, error } = await db.supabase
      .from('students')
      .delete()
      .eq('id', studentId)
      .select()
      .single();

    if (error) {
      console.error('删除学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '删除学生失败',
        error: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        success: false,
        message: '学生不存在'
      });
    }

    res.json({
      success: true,
      message: '学生删除成功'
    });

  } catch (error) {
    console.error('删除学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// ==================== 教师权限路由 ====================
// 使用独立的教师路由文件，支持分级管理和Excel导入
router.use('/teacher', teacherRoutes);

// 会话相关路由
router.post('/sessions/start', sessionController.startSession);
router.post('/sessions/end', sessionController.endSession);

// 日志相关路由
router.get('/logs', logController.getLogs);
router.post('/logs', logController.addLog);
router.delete('/logs', logController.deleteLogs);

// 文章相关路由 - 暂时注释掉，避免mysql2依赖问题
// router.get('/articles', articleController.getAllArticles);
// router.get('/articles/random', articleController.getRandomArticle);
// router.get('/articles/filter', articleController.getArticlesByFilter);
// router.get('/articles/:id', articleController.getArticleById);
// router.post('/articles', articleController.createArticle);
// router.put('/articles/:id', articleController.updateArticle);
// router.delete('/articles/:id', articleController.deleteArticle);

// 临时文章API端点 - 使用Supabase
router.get('/articles/filter', async (req, res) => {
  try {
    const { language } = req.query;
    console.log('请求文章筛选，语言:', language);

    // 重新导入数据库连接，确保使用最新的连接
    const db = require('../config/db');

    // 最简单的查询，先不筛选语言
    let query = db.supabase.from('articles').select('*');

    // 如果指定了语言，才添加筛选条件
    if (language) {
      query = query.eq('language', language);
    }

    const { data: articles, error } = await query;

    console.log('数据库查询结果:', { articles: articles?.length, error });

    if (error) {
      console.error('获取文章失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取文章失败',
        error: error.message,
        details: error
      });
    }

    res.status(200).json({
      success: true,
      articles: articles || []
    });
  } catch (error) {
    console.error('获取文章错误:', error);
    res.status(500).json({
      success: false,
      message: '获取文章失败',
      error: error.message
    });
  }
});

router.get('/articles', async (req, res) => {
  try {
    const db = require('../config/db');
    const { data: articles, error } = await db.supabase
      .from('articles')
      .select('*');

    if (error) {
      console.error('获取所有文章失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取文章失败',
        error: error.message
      });
    }

    res.status(200).json({
      success: true,
      articles: articles || []
    });
  } catch (error) {
    console.error('获取所有文章错误:', error);
    res.status(500).json({
      success: false,
      message: '获取文章失败',
      error: error.message
    });
  }
});

// 签到记录相关路由
router.get('/attendance', async (req, res) => {
  try {
    const db = require('../config/db');
    const records = await db.query('SELECT * FROM attendance ORDER BY id DESC LIMIT 50');

    console.log(`获取到 ${records.length} 条签到记录`);
    res.json({ data: records });
  } catch (err) {
    console.error('获取签到记录失败:', err);
    res.status(500).json({ error: '获取签到记录失败: ' + err.message });
  }
});

// 签到记录调试路由 - 验证表结构
router.get('/attendance/debug', async (req, res) => {
  try {
    const db = require('../config/db');

    // 检查表是否存在
    const tables = await db.query("SHOW TABLES LIKE 'attendance'");
    const tableExists = tables.length > 0;

    // 如果表存在，检查表结构
    let structure = null;
    if (tableExists) {
      structure = await db.query('DESCRIBE attendance');
    }

    // 检查外键约束
    const constraints = await db.query(`
      SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
      FROM information_schema.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'attendance'
        AND REFERENCED_TABLE_NAME IS NOT NULL
    `);

    // 返回调试信息
    res.json({
      table_exists: tableExists,
      table_structure: structure,
      constraints: constraints,
      database: process.env.DB_NAME || 'syxxstudent'
    });
  } catch (err) {
    console.error('获取签到表调试信息失败:', err);
    res.status(500).json({ error: '获取签到表调试信息失败: ' + err.message });
  }
});

// 学生端API路由
// 获取所有学校信息
router.get('/schools', async (req, res) => {
  try {
    const db = require('../config/db');
    const { data: schools, error } = await db.supabase
      .from('schools')
      .select('*')
      .order('name');

    if (error) {
      console.error('获取学校信息失败:', error);
      return res.status(500).json({ error: '获取学校信息失败' });
    }

    res.status(200).json(schools || []);
  } catch (error) {
    console.error('获取学校信息错误:', error);
    res.status(500).json({ error: '获取学校信息失败: ' + error.message });
  }
});

// 学生端 - 获取学校年级配置（无需权限验证）
router.get('/schools/:id/grades', async (req, res) => {
  try {
    const schoolId = req.params.id;
    const db = require('../config/db');

    const { data, error } = await db.supabase
      .from('school_grade_configs')
      .select('*')
      .eq('school_id', schoolId)
      .order('grade');

    if (error) {
      console.error('获取学校年级配置失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取学校年级配置失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('获取学校年级配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 获取学生信息（支持多种筛选条件）
router.get('/students', async (req, res) => {
  try {
    const db = require('../config/db');
    const { school_id, grade, class: className } = req.query;

    let query = db.supabase
      .from('students')
      .select('*');

    if (school_id) {
      query = query.eq('school_id', school_id);
    }

    if (grade) {
      query = query.eq('grade', parseInt(grade));
    }

    if (className) {
      query = query.eq('class', parseInt(className));
    }

    query = query.order('grade').order('class').order('name');

    const { data: students, error } = await query;

    if (error) {
      console.error('获取学生信息失败:', error);
      return res.status(500).json({ error: '获取学生信息失败' });
    }

    res.status(200).json(students || []);
  } catch (error) {
    console.error('获取学生信息错误:', error);
    res.status(500).json({ error: '获取学生信息失败: ' + error.message });
  }
});

// 记录学生签到
router.post('/student-signin', async (req, res) => {
  try {
    const db = require('../config/db');
    const { school_id, grade, class: className, name, seat_number } = req.body;

    // 验证必填字段
    if (!school_id || !grade || !className || !name) {
      return res.status(400).json({
        success: false,
        error: '学校、年级、班级和姓名为必填项'
      });
    }

    // 获取学校信息
    const { data: school, error: schoolError } = await db.supabase
      .from('schools')
      .select('name')
      .eq('id', school_id)
      .single();

    if (schoolError || !school) {
      console.error('查询学校信息失败:', schoolError);
      return res.status(404).json({
        success: false,
        error: '学校信息不存在'
      });
    }

    // 验证学生信息是否存在于数据库中
    const { data: student, error: studentError } = await db.supabase
      .from('students')
      .select('*')
      .eq('school_id', school_id)
      .eq('grade', parseInt(grade))
      .eq('class', parseInt(className))
      .eq('name', name.trim())
      .maybeSingle();

    if (studentError) {
      console.error('查询学生信息失败:', studentError);
      return res.status(500).json({
        success: false,
        error: '查询学生信息失败'
      });
    }

    if (!student) {
      return res.status(404).json({
        success: false,
        error: '学生信息不存在，请检查学校、年级、班级和姓名是否正确'
      });
    }

    // 记录签到信息
    const signin_time = new Date().toISOString();

    console.log('准备插入签到记录:', {
      student_identifier: student.student_identifier,
      student_id: student.id,
      signin_time,
      school_id: parseInt(school_id),
      grade: parseInt(grade),
      class: parseInt(className),
      name: name.trim(),
      seat_number: seat_number?.trim() || null
    });

    // 创建签到记录，使用名称而不是ID
    const signinRecord = {
      student_identifier: student.student_identifier || `${grade}_${className}_${name.trim()}`,
      student_id: student.id,
      school_name: school.name,
      grade_name: `${grade}年级`,
      class_name: `${className}班`,
      student_name: name.trim(),
      seat_number: seat_number?.trim() || null,
      signin_time
    };

    console.log('准备插入签到记录到student_signins表:', signinRecord);

    const { data: signin, error } = await db.supabase
      .from('student_signins')
      .insert(signinRecord)
      .select()
      .single();

    if (error) {
      console.error('记录签到失败:', error);
      console.error('错误详情:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      });

      // 如果数据库插入失败，至少记录到日志并返回成功
      // 这样用户可以继续使用系统
      console.log('数据库插入失败，但允许用户继续:', signinRecord);

      return res.status(200).json({
        success: true,
        message: '签到成功（记录已保存到日志）',
        data: {
          signin_time,
          student_info: student,
          note: '签到记录已保存到系统日志'
        }
      });
    }

    res.status(200).json({
      success: true,
      message: '签到成功',
      data: {
        ...signin,
        student_info: student
      }
    });
  } catch (error) {
    console.error('记录签到错误:', error);
    res.status(500).json({
      success: false,
      error: '记录签到失败: ' + error.message
    });
  }
});

// 获取打字文章列表
router.get('/typing-articles', async (req, res) => {
  try {
    const db = require('../config/db');
    const { data: articles, error } = await db.supabase
      .from('articles')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('获取打字文章失败:', error);
      return res.status(500).json({ error: '获取打字文章失败' });
    }

    res.status(200).json(articles || []);
  } catch (error) {
    console.error('获取打字文章错误:', error);
    res.status(500).json({ error: '获取打字文章失败: ' + error.message });
  }
});

// 测试articles表结构
router.get('/test/articles', async (req, res) => {
  try {
    const db = require('../config/db');

    // 先测试简单查询
    const { data: articles, error } = await db.supabase
      .from('articles')
      .select('*')
      .limit(1);

    if (error) {
      console.error('测试articles表失败:', error);
      return res.status(500).json({
        success: false,
        message: '测试articles表失败',
        error: error.message,
        details: error
      });
    }

    res.json({
      success: true,
      message: 'articles表测试成功',
      count: articles?.length || 0,
      sample: articles?.[0] || null,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('测试articles表错误:', error);
    res.status(500).json({
      success: false,
      message: '测试articles表错误',
      error: error.message
    });
  }
});



module.exports = router;