/**
 * 教师管理界面JavaScript - 支持分级管理和Excel导入
 * 实现学校->年级->班级的分级管理流程
 */

// 全局变量
let currentUser = null;
let currentSection = 'dashboard';
let teacherSchools = [];
let teacherStudents = [];
let teacherClasses = [];
let selectedSchool = null;
let selectedGrade = null;
let selectedClass = null;

// 请求去重缓存
const pendingRequests = new Map();

// 性能监控
function logPerformance(operation, startTime) {
  const duration = Date.now() - startTime;
  if (duration > 1000) {
    console.warn(`慢操作警告: ${operation} 耗时 ${duration}ms`);
  } else {
    console.log(`操作完成: ${operation} 耗时 ${duration}ms`);
  }
}

// 小工具：带超时的 fetch，避免界面长期“加载中”卡死
async function fetchWithTimeout(url, options = {}, timeoutMs = 3000) {
  // 请求去重：如果相同的请求正在进行中，直接返回Promise
  const requestKey = `${url}_${JSON.stringify(options)}`;
  if (pendingRequests.has(requestKey)) {
    console.log(`复用进行中的请求: ${url}`);
    return pendingRequests.get(requestKey);
  }

  const controller = new AbortController();
  const id = setTimeout(() => controller.abort('timeout'), timeoutMs);

  const requestPromise = (async () => {
    try {
      const resp = await fetch(url, { ...options, signal: controller.signal });
      return resp;
    } catch (e) {
      if (e.name === 'AbortError' || e === 'timeout') {
        console.warn(`请求超时: ${url} (${timeoutMs}ms)`);
        throw new Error(`请求超时，请检查网络连接`);
      }
      throw e;
    } finally {
      clearTimeout(id);
      pendingRequests.delete(requestKey);
    }
  })();

  pendingRequests.set(requestKey, requestPromise);
  return requestPromise;
}
// 统一鉴权与请求封装（Bearer + Cookie 双轨兼容）
function getAuthHeaders(extra) {
  const token = localStorage.getItem('token');
  const base = token ? { Authorization: `Bearer ${token}` } : {};
  return { ...(extra || {}), ...base };
}
function authFetch(url, options = {}, timeoutMs = 2000) {
  const startTime = Date.now();
  const opts = { cache: 'no-store', credentials: 'include', ...options, headers: getAuthHeaders(options.headers) };

  // 检测是否为Vercel环境
  const isVercel = window.location.hostname.includes('vercel.app') || window.location.hostname.includes('.xyz');

  // 根据环境和请求类型调整超时时间
  let actualTimeout = timeoutMs;
  if (isVercel) {
    // Vercel环境需要更长的超时时间
    if (url.includes('/students')) {
      actualTimeout = Math.max(timeoutMs, 10000); // 学生数据至少10秒
    } else if (url.includes('/grades')) {
      actualTimeout = Math.max(timeoutMs, 12000); // 年级数据至少12秒
    } else if (url.includes('/schools') || url.includes('/classes')) {
      actualTimeout = Math.max(timeoutMs, 8000); // 学校和班级数据至少8秒
    } else {
      actualTimeout = Math.max(timeoutMs, 6000); // 其他请求至少6秒
    }
  } else {
    // 本地环境使用较短的超时时间
    if (url.includes('/students')) {
      actualTimeout = 3000;
    } else if (url.includes('/schools') || url.includes('/classes')) {
      actualTimeout = 1500;
    }
  }

  const requestPromise = typeof actualTimeout === 'number' ?
    fetchWithTimeout(url, opts, actualTimeout) :
    fetch(url, opts);

  // 添加性能监控
  requestPromise.then(() => {
    logPerformance(`API请求: ${url}`, startTime);
  }).catch(() => {
    logPerformance(`API请求失败: ${url}`, startTime);
  });

  return requestPromise;
}

// 可见学校过滤：仅显示本人任教的学校（管理员不受限）
function getVisibleSchools(schools, classes) {
  const list = Array.isArray(schools) ? schools : [];
  try {
    if (currentUser && currentUser.role === 'admin') return list;
  } catch (_) {}
  const classSet = new Set((Array.isArray(classes) ? classes : []).map(c => c && c.school_id).filter(Boolean));

  // 修改逻辑：如果没有任教班级，或者在年级管理页面，显示所有学校
  // 这样新添加的学校也能在年级管理中显示，让教师可以设置任教班级
  if (classSet.size === 0 || currentSection === 'grade-management') {
    console.log('显示所有学校（无任教班级或在年级管理页面）');
    return list;
  }

  return list.filter(s => s && classSet.has(s.id));
}
// =============== 性能优化：加载状态管理 ===============

// 显示加载状态
function showLoadingState(type) {
    const loadingStates = {
        students: () => {
            const tbody = document.querySelector('#student-table-body');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center p-4">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                                <span>正在加载学生数据...</span>
                            </div>
                        </td>
                    </tr>
                `;
            }
        },
        schools: () => {
            const container = document.getElementById('teacher-content-area');
            if (container && container.innerHTML.includes('学校管理')) {
                const schoolsContainer = container.querySelector('.row');
                if (schoolsContainer) {
                    schoolsContainer.innerHTML = `
                        <div class="col-12 text-center p-5">
                            <div class="spinner-border text-primary mb-3" role="status"></div>
                            <div>正在加载学校数据...</div>
                        </div>
                    `;
                }
            }
        },
        grades: (schoolId) => {
            const container = document.getElementById(`grades-container-${schoolId}`);
            if (container) {
                container.innerHTML = `
                    <div class="text-center p-3">
                        <div class="spinner-border spinner-border-sm text-info" role="status"></div>
                        <small class="ms-2">加载中...</small>
                    </div>
                `;
            }
        }
    };

    if (loadingStates[type]) {
        loadingStates[type]();
    }
}

// 隐藏加载状态
function hideLoadingState(type) {
    // 加载状态会在数据渲染时自动被替换，这里主要用于错误处理
    if (type === 'students') {
        const tbody = document.querySelector('#student-table-body');
        if (tbody && tbody.innerHTML.includes('正在加载学生数据')) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted p-4">
                        <i class="fas fa-exclamation-triangle mb-2"></i><br>
                        加载失败，请刷新重试
                    </td>
                </tr>
            `;
        }
    }
}

// 防抖函数 - 优化搜索和筛选性能
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数 - 优化滚动和频繁操作
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// =============== 数据缓存机制 ===============

// 缓存管理
const dataCache = {
    students: { data: null, timestamp: 0, ttl: 5 * 60 * 1000 }, // 5分钟缓存
    schools: { data: null, timestamp: 0, ttl: 10 * 60 * 1000 }, // 10分钟缓存
    grades: new Map() // 年级数据按学校ID缓存
};

// 检查缓存是否有效
function isCacheValid(cacheKey) {
    const cache = dataCache[cacheKey];
    if (!cache || !cache.data) return false;
    return (Date.now() - cache.timestamp) < cache.ttl;
}

// 设置缓存
function setCache(cacheKey, data) {
    if (dataCache[cacheKey]) {
        dataCache[cacheKey].data = data;
        dataCache[cacheKey].timestamp = Date.now();
    }
}

// 获取缓存
function getCache(cacheKey) {
    return isCacheValid(cacheKey) ? dataCache[cacheKey].data : null;
}

// 清除缓存
function clearCache(cacheKey) {
    if (cacheKey) {
        if (dataCache[cacheKey]) {
            dataCache[cacheKey].data = null;
            dataCache[cacheKey].timestamp = 0;
        }
    } else {
        // 清除所有缓存
        Object.keys(dataCache).forEach(key => {
            if (key !== 'grades') {
                dataCache[key].data = null;
                dataCache[key].timestamp = 0;
            }
        });
        dataCache.grades.clear();
    }
}

// =============== 性能监控 ===============

// 性能计时器
const performanceTimer = {
    start(label) {
        console.time(label);
    },
    end(label) {
        console.timeEnd(label);
    }
};

// 页面加载性能优化
document.addEventListener('DOMContentLoaded', function() {
    // 预加载关键数据
    if (localStorage.getItem('token')) {
        // 异步预加载学校数据（如果缓存中没有）
        if (!getCache('schools')) {
            loadTeacherSchoolsFromAPI().then(schools => {
                if (schools) {
                    setCache('schools', schools);
                }
            }).catch(console.error);
        }
    }
});

// 教师端年级管理不再维护总班级数与徽章，移除旧徽章/订阅逻辑（已清理）
// 简单的消息显示函数
function showMessage(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);

    // 尝试使用Utils，如果不存在则使用简单的toast或alert
    if (typeof Utils !== 'undefined' && Utils.showMessage) {
        Utils.showMessage(message, type);
    } else {
        // 创建简单的toast消息
        createToast(message, type);
    }
}

/**
 * 创建简单的toast消息
 */
function createToast(message, type) {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        `;
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toast = document.createElement('div');
    const bgColor = type === 'success' ? '#28a745' :
                   type === 'error' ? '#dc3545' :
                   type === 'warning' ? '#ffc107' : '#007bff';

    toast.style.cssText = `
        background-color: ${bgColor};
        color: white;
        padding: 12px 20px;
        border-radius: 5px;
        margin-bottom: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    toast.textContent = message;

    toastContainer.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 10);

    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 初始化函数已移至HTML中统一管理，避免重复调用

/**
 * 初始化教师管理界面
 */
async function initTeacherManagement() {
    console.log('=== 初始化教师管理界面 ===');

    try {
        // 检查必要的DOM元素
        const contentArea = document.getElementById('teacher-content-area');
        if (!contentArea) {
            console.error('找不到teacher-content-area元素');
            alert('页面加载错误：找不到内容区域');
            return;
        }

        // 强制登录检查
        const token = localStorage.getItem('token');
        console.log('Token检查:', {
            exists: !!token,
            length: token ? token.length : 0,
            preview: token ? token.substring(0, 20) + '...' : 'null'
        });

        if (!token) {
            console.log('未找到token，强制重定向到登录页面');
            alert('请先在主页面登录后再访问教师管理页面');
            window.location.href = '/';
            return;
        }

        // 验证token并获取用户信息
        console.log('开始验证token...');
        const user = await validateTeacherToken();
        console.log('Token验证结果:', user);

        if (!user) {
            console.log('token验证失败，强制重定向到登录页面');
            alert('登录已过期，请重新登录');
            localStorage.clear(); // 清除所有本地数据
            window.location.href = '/';
            return;
        }

        currentUser = user;
        console.log('用户验证成功:', currentUser);

        // 确保用户有教师权限
        if (currentUser.role !== 'teacher' && currentUser.role !== 'admin') {
            console.log('用户权限不足，需要教师或管理员权限');
            alert('您没有访问教师管理页面的权限');
            window.location.href = '/';
            return;
        }

        console.log('当前用户设置完成:', currentUser);

        // 更新用户信息显示
        console.log('更新用户信息显示...');
        updateTeacherUserInfo();

        // 加载教师的基础数据
        console.log('开始加载基础数据...');
        await loadTeacherBaseData();
        console.log('基础数据加载完成');

        // 延迟启动实时同步，优先显示界面
        setTimeout(() => {
            console.log('启动实时数据同步...');
            if (typeof SchoolRealtimeSync !== 'undefined') {
                SchoolRealtimeSync.start().then((success) => {
                    if (success) {
                        console.log('实时数据同步已启动');
                    } else {
                        console.warn('实时数据同步启动失败，将使用手动刷新模式');
                    }
                }).catch(error => {
                    console.error('启动实时数据同步失败:', error);
                    console.warn('将使用手动刷新模式');
                });
            } else {
                console.warn('SchoolRealtimeSync未定义，将使用手动刷新模式');
            }
        }, 500); // 延迟500ms启动，让界面先显示

        // 显示默认页面
        console.log('显示默认页面...');
        showTeacherSection('dashboard');

        // 隐藏加载状态
        const loadingElement = document.querySelector('.text-center.py-5');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        console.log('初始化完成');

    } catch (error) {
        console.error('初始化教师管理界面失败:', error);
        console.error('错误堆栈:', error.stack);

        // 显示基本界面而不是错误
        currentUser = {
            username: 'admin',
            display_name: '管理员',
            role: 'admin'
        };
        updateTeacherUserInfo();

        // 设置空数据并显示界面
        teacherSchools = [];
        teacherStudents = [];
        teacherClasses = [];

        showTeacherSection('dashboard');

        console.log('使用备用初始化方案完成');
    }
}

/**
 * 验证教师token - 简化版本
 */
async function validateTeacherToken() {
    console.log('开始验证token...');

    const token = localStorage.getItem('token');

    if (!token) {
        console.log('没有找到token');
        return null;
    }

    try {
        console.log('验证token:', token.substring(0, 20) + '...');

        const response = await fetch('/api/auth/validate', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Token验证成功:', result);
            const user = result.user;

            // 更新本地用户信息
            localStorage.setItem('user', JSON.stringify(user));
            return user;
        } else {
            console.log('Token验证失败，状态码:', response.status);
            const errorResult = await response.json().catch(() => ({}));
            console.log('错误详情:', errorResult);

            // 清除无效token
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            return null;
        }
    } catch (error) {
        console.error('Token验证请求失败:', error);
        // 清除可能损坏的token
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        return null;
    }
}

/**
 * 加载教师的基础数据
 */
async function loadTeacherBaseData() {
    console.log('=== 加载教师基础数据 ===');

    const startTime = Date.now();

    // 检查缓存，避免重复加载
    const hasSchools = teacherSchools && teacherSchools.length > 0;
    const hasStudents = teacherStudents && teacherStudents.length > 0;
    const hasClasses = teacherClasses && teacherClasses.length > 0;

    console.log(`缓存状态: 学校${hasSchools ? '✓' : '✗'} 学生${hasStudents ? '✓' : '✗'} 班级${hasClasses ? '✓' : '✗'}`);

    // 设置默认的空数组，确保界面能正常显示
    if (!hasSchools) teacherSchools = [];
    if (!hasStudents) teacherStudents = [];
    if (!hasClasses) teacherClasses = [];

    try {
        // 只加载缺失的数据，减少API调用
        const promises = [];

        if (!hasSchools) {
            promises.push(
                loadTeacherSchoolsFromAPI().catch(err => {
                    console.error('加载学校数据失败:', err);
                    return [];
                }).then(data => { teacherSchools = data || []; return data; })
            );
        }

        if (!hasStudents) {
            promises.push(
                loadTeacherStudentsFromAPI().catch(err => {
                    console.error('加载学生数据失败:', err);
                    return [];
                }).then(data => { teacherStudents = data || []; return data; })
            );
        }

        if (!hasClasses) {
            promises.push(
                loadTeacherClassesFromAPI().catch(err => {
                    console.error('加载班级数据失败:', err);
                    return [];
                }).then(data => { teacherClasses = data || []; return data; })
            );
        }

        // 等待所有缺失数据加载完成
        if (promises.length > 0) {
            await Promise.all(promises);
            console.log(`新数据加载耗时: ${Date.now() - startTime}ms`);
        } else {
            console.log('使用缓存数据，无需重新加载');
        }

        // 数据已在上面的promises中直接赋值，这里不需要重复赋值

        console.log('基础数据加载结果:');
        console.log('- 学校数量:', teacherSchools.length);
        console.log('- 学生数量:', teacherStudents.length);
        console.log('- 班级数量:', teacherClasses.length);

    } catch (error) {
        console.error('加载教师基础数据时发生错误:', error);
        // 即使出错也要确保有默认数据
        teacherSchools = [];
        teacherStudents = [];
        teacherClasses = [];
        console.log('使用空数据继续');
    }

    console.log('基础数据加载完成');
}

/**
 * 加载教师任教学校数据
 */
async function loadTeacherSchoolsFromAPI() {
    try {
        const url = `/api/teacher/schools?ts=${Date.now()}`;
        const response = await authFetch(url, { });

        if (response.ok) {
            const schools = await response.json();
            console.log('从API加载学校数据成功:', schools);
            // 确保返回的是数组
            return Array.isArray(schools) ? schools : (schools.data || []);
        } else {
            console.log('获取教师学校数据失败，状态码:', response.status);
            return [];
        }
    } catch (error) {
        console.error('加载教师学校数据失败:', error);
        return [];
    }
}

/**
 * 加载教师任教学生数据
 */
async function loadTeacherStudentsFromAPI(forceRefresh = false) {
    try {
        console.log('加载学生数据，强制刷新:', forceRefresh);

        // 显示加载状态
        showLoadingState('students');

        // 从服务器获取最新数据，仅在强制刷新时添加防缓存措施
        let url = '/api/teacher/students';
        let options = {};

        if (forceRefresh) {
            url += `?ts=${Date.now()}`;
            options = {
                cache: 'no-store',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate'
                }
            };
        }

        const response = await authFetch(url, options, 12000); // 为学生数据设置12秒超时

        if (response.ok) {
            const students = await response.json();
            const result = Array.isArray(students) ? students : (students.data || []);

            console.log(`从服务器获取到 ${result.length} 个学生数据`);

            // 隐藏加载状态
            hideLoadingState('students');

            return result;
        } else {
            console.error('获取教师学生数据失败，状态码:', response.status);
            hideLoadingState('students');
            return [];
        }
    } catch (error) {
        console.error('加载教师学生数据失败:', error);
        hideLoadingState('students');
        return [];
    }
}

/**
 * 加载教师班级数据
 */
async function loadTeacherClassesFromAPI() {
    try {
        const response = await fetch(`/api/teacher/classes`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const classes = await response.json();
            console.log('从API加载班级数据成功:', classes);
            // 确保返回的是数组
            return Array.isArray(classes) ? classes : (classes.data || []);
        } else {
            console.log('获取教师班级数据失败，状态码:', response.status);
            return [];
        }
    } catch (error) {
        console.error('加载教师班级数据失败:', error);
        return [];
    }
}

/**
 * 从年级配置汇总班级总数：sum(class_count)
 */
async function sumClassesFromConfigs() {
    try {
        let total = 0;
        for (const school of teacherSchools || []) {
            const resp = await fetch(`/api/teacher/schools/${school.id}/grades`, {
                headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
            });
            if (!resp.ok) continue;
            const result = await resp.json();
            if (result && result.success && Array.isArray(result.data)) {
                total += result.data.reduce((acc, c) => acc + (parseInt(c.class_count) || 0), 0);
            }
        }
        return total;
    } catch (e) {
        console.warn('统计班级数量失败:', e);
        return 0;
    }
}


/**
 * 更新用户信息显示
 */
function updateTeacherUserInfo() {
    const displayNameElement = document.getElementById('user-display-name');
    const roleElement = document.getElementById('user-role');

    if (displayNameElement && currentUser) {
        displayNameElement.textContent = currentUser.display_name || currentUser.username || '教师';
    }

    if (roleElement && currentUser) {
        roleElement.textContent = currentUser.role === 'admin' ? '管理员' : '教师';
    }
}

/**
 * 显示教师界面的不同部分
 */
function showTeacherSection(section) {
    console.log('显示教师界面部分:', section);

    // 如从年级管理离开，先停止年级配置实时订阅
    // 教师端年级管理已移除实时订阅，无需处理

    // 更新导航状态
    updateTeacherNavigation(section);

    // 显示对应内容
    const contentArea = document.getElementById('teacher-content-area');
    if (!contentArea) {
        console.error('找不到内容区域');
        return;
    }

    currentSection = section;

    switch (section) {
        case 'dashboard':
            showTeacherDashboard();
            break;
        case 'school-management':
            showSchoolManagement();
            break;
        case 'grade-management':
            showGradeManagement();
            break;
        case 'student-management':
            showStudentManagement();
            break;
        case 'profile':
            showTeacherProfile();
            break;
        default:
            console.error('未知的页面部分:', section);
            showTeacherDashboard();
    }
}

/**
 * 更新导航状态
 */
function updateTeacherNavigation(activeSection) {
    // 移除所有导航链接的active类
    document.querySelectorAll('.admin-sidebar .nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 为当前活动的导航链接添加active类
    const activeLink = document.querySelector(`.admin-sidebar .nav-link[onclick*="${activeSection}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

/**
 * 显示教师控制台概览
 */
async function showTeacherDashboard() {
    const contentArea = document.getElementById('teacher-content-area');

    // 确保任教班级数据为最新（来源于年级管理中的勾选保存）
    try {
        teacherClasses = await loadTeacherClassesFromAPI();
    } catch (e) { console.warn('刷新任教班级失败', e); }

    // 安全地获取数组长度
    const schoolsCount = Array.isArray(teacherSchools) ? teacherSchools.length : 0;
    const studentsCount = Array.isArray(teacherStudents) ? teacherStudents.length : 0;
    const classesCount = Array.isArray(teacherClasses) ? teacherClasses.length : 0;

    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-tachometer-alt"></i> 控制台概览
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">欢迎使用教师管理中心</p>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                    <div class="card-body text-center">
                        <i class="fas fa-school fa-2x mb-2"></i>
                        <h3 class="mb-1">${schoolsCount}</h3>
                        <p class="mb-0">任教学校</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white;">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3 class="mb-1">${studentsCount}</h3>
                        <p class="mb-0">管理学生</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: white;">
                    <div class="card-body text-center">
                        <i class="fas fa-chalkboard fa-2x mb-2"></i>
                        <h3 class="mb-1">${classesCount}</h3>
                        <p class="mb-0">任教班级</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white;">
                    <div class="card-body text-center">
                        <i class="fas fa-tasks fa-2x mb-2"></i>
                        <h3 class="mb-1">0</h3>
                        <p class="mb-0">待处理任务</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <h3 style="margin-bottom: 20px; color: #333;">
                <i class="fas fa-bolt"></i> 快速操作
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <button class="quick-action-btn" onclick="showTeacherSection('school-management')" style="padding: 15px; border: none; border-radius: 10px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; cursor: pointer; transition: transform 0.3s ease;">
                    <i class="fas fa-school"></i> 学校管理
                </button>
                <button class="quick-action-btn" onclick="showTeacherSection('grade-management')" style="padding: 15px; border: none; border-radius: 10px; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; cursor: pointer; transition: transform 0.3s ease;">
                    <i class="fas fa-layer-group"></i> 年级管理
                </button>
                <button class="quick-action-btn" onclick="showTeacherSection('student-management')" style="padding: 15px; border: none; border-radius: 10px; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; cursor: pointer; transition: transform 0.3s ease;">
                    <i class="fas fa-users"></i> 学生管理
                </button>
            </div>
        </div>
    `;

    // 添加快速操作按钮的悬停效果
    document.querySelectorAll('.quick-action-btn').forEach(btn => {
        btn.addEventListener('mouseenter', () => {
            btn.style.transform = 'translateY(-3px)';
            btn.style.boxShadow = '0 6px 20px rgba(40, 167, 69, 0.3)';
        });
        btn.addEventListener('mouseleave', () => {
            btn.style.transform = 'translateY(0)';
            btn.style.boxShadow = 'none';
        });
    });
}

// 删除了重复的 showTeacherSchoolsManagement 函数，使用 showSchoolManagement 代替

// 删除了重复的 renderTeacherSchoolsTable 函数，使用 renderSchoolTableRows 代替

/**
 * 格式化日期时间
 */
function formatDateTime(dateString) {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour12: false});
    } catch (e) {
        return dateString;
    }
}

/**
 * 显示教师学生管理
 */
async function showTeacherStudentsManagement() {
    console.log('=== 显示教师学生管理页面 ===');

    // 确保学生数据已加载
    if (!teacherStudents || teacherStudents.length === 0) {
        console.log('重新加载教师学生数据...');
        teacherStudents = await loadTeacherStudentsFromAPI();
    }

    console.log('教师学生数据:', teacherStudents);

    const contentArea = document.getElementById('teacher-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-users"></i> 学生管理
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">管理您任教班级的学生信息</p>
            </div>
            <button onclick="showAddTeacherStudentForm()" class="btn btn-primary">
                <i class="fas fa-plus"></i> 添加学生
            </button>
        </div>

        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学生姓名</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学校</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">年级班级</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学号</th>
                        <th style="padding: 15px; text-align: center; font-weight: 600; color: #495057;">操作</th>
                    </tr>
                </thead>
                <tbody id="teacher-students-table-body">
                    ${renderTeacherStudentsTable()}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * 渲染教师学生表格
 */
function renderTeacherStudentsTable() {
    if (!teacherStudents || teacherStudents.length === 0) {
        return `
            <tr>
                <td colspan="5" style="padding: 30px; text-align: center; color: #666;">
                    <i class="fas fa-users fa-2x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>暂无学生数据</p>
                    <p style="font-size: 14px; opacity: 0.7;">点击上方"添加学生"按钮开始添加</p>
                </td>
            </tr>
        `;
    }

    return teacherStudents.map(student => {
        // 处理不同的数据格式
        const studentName = student.name || student.student_name || '未知';
        const studentGrade = student.grade || student.student_grade || '未知';
        const studentClass = student.class || student.student_class || '未知';
        const studentId = student.student_identifier || student.student_id || student.id || '未设置';
        const schoolId = student.school_id || student.school || '';

        // 查找学校名称
        const school = teacherSchools ? teacherSchools.find(s => s.id == schoolId) : null;
        const schoolName = school ? school.name : '未知学校';

        return `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 15px;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user text-primary me-2"></i>
                        <strong>${studentName}</strong>
                    </div>
                </td>
                <td style="padding: 15px;">
                    <span class="badge bg-light text-dark">${schoolName}</span>
                </td>
                <td style="padding: 15px;">
                    ${studentGrade}年级${studentClass}班
                </td>
                <td style="padding: 15px;">
                    <code>${studentId}</code>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <button onclick="editTeacherStudent('${student.id}')" class="btn btn-sm btn-success me-1">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button onclick="deleteTeacherStudent('${student.id}', '${studentName}')" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// 班级管理功能已删除，现在在年级管理中统一配置

/**
 * 显示教师个人设置
 */
function showTeacherProfile() {
    const contentArea = document.getElementById('teacher-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-user-cog"></i> 个人设置
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">管理您的个人信息和偏好设置</p>
            </div>
        </div>

        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <p class="text-center text-muted py-4">
                <i class="fas fa-tools fa-2x mb-3"></i><br>
                个人设置功能正在开发中...
            </p>
        </div>
    `;
}

/**
 * 教师退出登录
 */
function teacherLogout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/';
    }
}

/**
 * 跳转到登录页面
 */
function redirectToLogin() {
    window.location.href = '/';
}

/**
 * 显示不同的功能区域（保留原函数）
 */
async function showSection(section) {
    // 统一路由到教师端界面，避免“正在加载”卡住
    const map = {
        dashboard: 'dashboard',
        schools: 'school-management',
        classes: 'grade-management',
        students: 'student-management',
        import: 'student-management'
    };
    const target = map[section] || 'dashboard';
    showTeacherSection(target);
    if (section === 'import') {
        setTimeout(() => {
            if (typeof showExcelImportModal === 'function') showExcelImportModal();
        }, 0);
    }
}

/**
 * 显示管理概览
 */
async function showDashboard() {
    const contentArea = document.getElementById('teacher-content-area');

    try {
        // 使用已缓存的数据，避免重复API调用
        const totalStudents = (teacherStudents || []).length;
        const totalClasses = (teacherClasses || []).length;
        const totalSchools = (teacherSchools || []).length;

        // 如果缓存数据为空，显示加载状态并异步加载
        if (totalStudents === 0 && totalClasses === 0 && totalSchools === 0) {
            contentArea.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="text-muted mt-2">正在加载统计数据...</p>
                </div>
            `;

            // 异步加载基础数据
            loadTeacherBaseData().then(() => {
                // 数据加载完成后重新渲染
                showDashboard();
            }).catch(error => {
                console.error('加载基础数据失败:', error);
                contentArea.innerHTML = `
                    <div class="alert alert-warning text-center">
                        <h4>数据加载失败</h4>
                        <p>无法获取统计数据，请刷新页面重试</p>
                        <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
                    </div>
                `;
            });
            return;
        }

        contentArea.innerHTML = `
            <h2><i class="fas fa-tachometer-alt"></i> 管理概览</h2>

            <div class="row">
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3>${totalSchools}</h3>
                        <p><i class="fas fa-school"></i> 关联学校</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3>${totalClasses}</h3>
                        <p><i class="fas fa-chalkboard"></i> 任教班级</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3>${totalStudents}</h3>
                        <p><i class="fas fa-users"></i> 管理学生</p>
                    </div>
                </div>
            </div>

            <div class="management-card">
                <h4><i class="fas fa-tools"></i> 快速操作</h4>
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-primary btn-custom w-100" onclick="showSection('schools')">
                            <i class="fas fa-school"></i><br>学校管理
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success btn-custom w-100" onclick="showSection('classes')">
                            <i class="fas fa-chalkboard"></i><br>班级管理
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-info btn-custom w-100" onclick="showSection('students')">
                            <i class="fas fa-users"></i><br>学生管理
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning btn-custom w-100" onclick="showSection('import')">
                            <i class="fas fa-file-import"></i><br>学生导入
                        </button>
                    </div>
                </div>
            </div>
        `;

    } catch (error) {
        contentArea.innerHTML = `<div class="error-message">加载概览数据失败: ${error.message}</div>`;
    }
}

/**
 * 显示学校管理
 */
async function showSchools() {
    const contentArea = document.getElementById('content-area');

    try {
        // 获取所有学校（用于选择）
        const allSchoolsResponse = await fetch('/api/schools', {
            headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        });

        let allSchools = [];
        if (allSchoolsResponse.ok) {
            const result = await allSchoolsResponse.json();
            allSchools = result.data || [];
        }

        // 构建学校列表
        let schoolsHtml = '';
        if (allSchools.length === 0) {
            schoolsHtml = '<div class="alert alert-info">暂无学校数据</div>';
        } else {
            schoolsHtml = allSchools.map(school => {
                const isAssociated = userSchools.some(us => us.id === school.id);
                return `
                    <div class="school-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6><i class="fas fa-school"></i> ${school.name}</h6>
                                <small>${school.address || '地址未填写'}</small>
                            </div>
                            <div>
                                ${isAssociated ?
                                    '<span class="badge bg-success">已关联</span>' :
                                    `<button class="btn btn-light btn-sm" onclick="associateSchool(${school.id})">
                                        <i class="fas fa-plus"></i> 关联
                                    </button>`
                                }
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        contentArea.innerHTML = `
            <h2><i class="fas fa-school"></i> 学校管理</h2>

            <div class="management-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>学校列表</h5>
                    <button class="btn btn-primary btn-custom" onclick="showAddSchoolModal()">
                        <i class="fas fa-plus"></i> 添加学校
                    </button>
                </div>

                ${schoolsHtml}
            </div>
        `;

    } catch (error) {
        contentArea.innerHTML = `<div class="error-message">加载学校管理失败: ${error.message}</div>`;
    }
}

// 旧的班级管理函数已删除，现在在年级管理中统一配置班级

/**
 * 显示学生管理
 */
async function showStudents() {
    const contentArea = document.getElementById('content-area');

    try {
        const response = await fetch('/api/students', {
            headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        });

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || '获取学生信息失败');
        }

        const students = result.data || [];

        let studentsHtml = '';
        if (students.length === 0) {
            studentsHtml = '<tr><td colspan="5" class="text-center">没有找到学生数据</td></tr>';
        } else {
            studentsHtml = students.map(student => `
                <tr>
                    <td>${student.student_identifier}</td>
                    <td>${student.name}</td>
                    <td>${student.grade}年级${student.class}班</td>
                    <td>${student.medal_count || 0}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editStudent(${student.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteStudent(${student.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        contentArea.innerHTML = `
            <h2><i class="fas fa-users"></i> 学生管理</h2>

            <div class="table-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>学生列表 (${students.length}人)</h5>
                    <button class="btn btn-success btn-custom" onclick="showSection('import')">
                        <i class="fas fa-file-import"></i> 批量导入
                    </button>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>学号</th>
                                <th>姓名</th>
                                <th>班级</th>
                                <th>奖章数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${studentsHtml}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

    } catch (error) {
        contentArea.innerHTML = `<div class="error-message">加载学生管理失败: ${error.message}</div>`;
    }
}

/**
 * 显示学生导入
 */
async function showImport() {
    const contentArea = document.getElementById('content-area');

    contentArea.innerHTML = `
        <h2><i class="fas fa-file-import"></i> 学生导入</h2>

        <div class="management-card">
            <h5>批量导入学生</h5>
            <p class="text-muted">支持Excel文件(.xlsx, .xls)和CSV文件(.csv)，请确保文件包含"学号"和"姓名"列。</p>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="importSchool" class="form-label">选择学校 *</label>
                    <select class="form-select" id="importSchool" onchange="loadSchoolClasses()">
                        <option value="">请选择学校</option>
                        ${userSchools.map(school => `<option value="${school.id}">${school.name}</option>`).join('')}
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="importGrade" class="form-label">年级 *</label>
                    <input type="number" class="form-control" id="importGrade" min="1" max="12">
                </div>
                <div class="col-md-4">
                    <label for="importClass" class="form-label">班级 *</label>
                    <input type="number" class="form-control" id="importClass" min="1" max="50">
                </div>
            </div>

            <div class="file-upload-area" id="fileUploadArea">
                <i class="fas fa-cloud-upload-alt fa-3x mb-3"></i>
                <h5>拖拽文件到此处或点击选择文件</h5>
                <p class="text-muted">支持 .xlsx, .xls, .csv 格式</p>
                <input type="file" id="fileInput" accept=".xlsx,.xls,.csv" style="display: none;">
                <button class="btn btn-primary btn-custom" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-folder-open"></i> 选择文件
                </button>
            </div>

            <div id="importPreview" style="display: none;">
                <h6 class="mt-4">导入预览</h6>
                <div id="previewContent"></div>
                <button class="btn btn-success btn-custom mt-3" onclick="confirmImport()">
                    <i class="fas fa-check"></i> 确认导入
                </button>
            </div>
        </div>
    `;

    // 绑定文件上传事件
    setupFileUpload();
}

/**
 * 设置文件上传
 */
function setupFileUpload() {
    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.getElementById('fileUploadArea');

    fileInput.addEventListener('change', handleFileSelect);

    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect({ target: { files } });
        }
    });
}

/**
 * 处理文件选择
 */
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 这里应该解析文件内容并显示预览
    // 由于涉及文件解析，这里先显示一个简单的预览
    const preview = document.getElementById('importPreview');
    const content = document.getElementById('previewContent');

    content.innerHTML = `
        <div class="alert alert-info">
            <strong>文件:</strong> ${file.name}<br>
            <strong>大小:</strong> ${(file.size / 1024).toFixed(2)} KB<br>
            <strong>类型:</strong> ${file.type}
        </div>
        <p class="text-muted">文件解析功能需要后端支持，这里显示文件基本信息。</p>
    `;

    preview.style.display = 'block';
}

/**
 * 退出登录
 */
function logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    redirectToLogin();
}

// 删除重复的redirectToLogin函数

/**
 * 显示消息
 */
function showMessage(message, type = 'info') {
    console.log('显示消息:', message, type);

    try {
        const alertClass = {
            'success': 'alert alert-success alert-dismissible fade show',
            'error': 'alert alert-danger alert-dismissible fade show',
            'warning': 'alert alert-warning alert-dismissible fade show',
            'info': 'alert alert-info alert-dismissible fade show'
        }[type] || 'alert alert-info alert-dismissible fade show';

        const messageDiv = document.createElement('div');
        messageDiv.className = alertClass;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            min-width: 250px;
        `;
        messageDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' :
                                   type === 'error' ? 'times-circle' :
                                   type === 'warning' ? 'exclamation-triangle' :
                                   'info-circle'} me-2"></i>
                <div>${message}</div>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;

        // 直接添加到body，避免找不到父元素的问题
        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);

        console.log('消息显示成功');

    } catch (error) {
        console.error('显示消息时出错:', error);
        // 如果显示消息失败，至少在控制台输出
        console.log(`[${type.toUpperCase()}] ${message}`);

        // 备用方案：使用浏览器原生alert
        if (type === 'error') {
            alert(`错误: ${message}`);
        } else if (type === 'success') {
            alert(`成功: ${message}`);
        }
    }
}

// ==================== 学校管理模态框函数 ====================

// 存储当前编辑/删除的学校ID
let currentSchoolId = null;

/**
 * 移除可能存在的modal-backdrop
 */
function removeModalBackdrop() {
    // 移除所有可能存在的backdrop
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => {
        backdrop.remove();
    });

    // 移除body上的modal-open类
    document.body.classList.remove('modal-open');

    // 重置body的样式
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
}

/**
 * 显示添加学校模态框
 */
function showAddSchoolModal() {
    // 首先移除可能存在的backdrop
    removeModalBackdrop();

    // 清空学校名称字段
    const schoolNameInput = document.getElementById('schoolName');
    if (schoolNameInput) {
        schoolNameInput.value = '';
    }

    // 使用标准的Bootstrap方式显示模态框
    const modalElement = document.getElementById('addSchoolModal');
    if (modalElement) {
        // 移除任何存在的旧实例
        const existingModal = bootstrap.Modal.getInstance(modalElement);
        if (existingModal) {
            existingModal.dispose();
        }

        const modal = new bootstrap.Modal(modalElement, {
            backdrop: true,  // 允许点击背景关闭
            keyboard: true,  // 允许ESC键关闭
            focus: true      // 自动聚焦
        });

        modal.show();

        // 模态框显示后聚焦到输入框
        modalElement.addEventListener('shown.bs.modal', function () {
            const input = document.getElementById('schoolName');
            if (input) {
                input.focus();
            }
        }, { once: true });

        // 模态框隐藏后清理backdrop
        modalElement.addEventListener('hidden.bs.modal', function () {
            removeModalBackdrop();
        }, { once: true });

    } else {
        console.error('找不到addSchoolModal元素');
        showMessage('模态框加载失败，请刷新页面重试', 'error');
    }
}

/**
 * 提交添加学校
 */
async function submitAddSchool() {
    const schoolName = document.getElementById('schoolName').value.trim();

    if (!schoolName) {
        showMessage('请输入学校名称', 'error');
        return;
    }

    console.log('准备添加学校:', schoolName);

    // 显示加载状态
    const submitBtn = document.querySelector('#addSchoolModal .btn-success');
    if (!submitBtn) {
        console.error('找不到提交按钮');
        showMessage('界面错误，请刷新页面重试', 'error');
        return;
    }
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 添加中...';
    submitBtn.disabled = true;

    try {
        // 检查token状态
        const token = localStorage.getItem('token');
        console.log('当前token状态:', {
            exists: !!token,
            length: token ? token.length : 0,
            preview: token ? token.substring(0, 20) + '...' : 'null'
        });

        if (!token) {
            throw new Error('没有找到认证token，请重新登录');
        }

        const response = await fetch('/api/teacher/schools', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ name: schoolName })
        });

        const result = await response.json();
        console.log('添加学校完整响应:', {
            status: response.status,
            ok: response.ok,
            result: result
        });

        if (response.ok && (result.success || result.data)) {
            console.log('学校添加成功');

            // 检查是否有警告信息
            if (result.warning) {
                showMessage(`${result.message || '学校添加成功'} (${result.warning})`, 'warning');
            } else {
                showMessage(result.message || '学校添加成功', 'success');
            }

            // 关闭模态框
            const modalInstance = bootstrap.Modal.getInstance(document.getElementById('addSchoolModal'));
            if (modalInstance) {
                modalInstance.hide();
            }

            // 清空表单
            document.getElementById('schoolName').value = '';

            // 立即刷新学校数据（确保界面更新）
            console.log('立即刷新学校数据以更新界面...');
            try {
                const freshSchools = await loadTeacherSchoolsFromAPI();
                teacherSchools = freshSchools;
                console.log('学校数据已刷新:', teacherSchools);

                // 如果当前在学校管理页面，立即更新显示
                if (currentSection === 'school-management') {
                    updateSchoolManagementDisplay();
                }

                // 如果当前在年级管理页面，也立即更新显示
                if (currentSection === 'grade-management') {
                    console.log('当前在年级管理页面，立即更新显示新学校');
                    await showGradeManagement();
                }

                console.log('界面已更新');
            } catch (refreshError) {
                console.error('刷新学校数据失败:', refreshError);
                // 即使刷新失败，也不影响添加成功的提示
            }

        } else {
            console.error('学校添加失败，详细信息:', {
                status: response.status,
                statusText: response.statusText,
                result: result
            });

            // 显示详细的错误信息
            let errorMessage = '添加学校失败';

            // 根据HTTP状态码提供更具体的错误信息
            if (response.status === 403) {
                errorMessage = '权限不足：请确认您已正确登录且有添加学校的权限';
            } else if (response.status === 401) {
                errorMessage = '认证失败：请重新登录';
            } else if (result.message) {
                errorMessage = result.message;
            } else if (result.error) {
                errorMessage = result.error;
            }

            // 如果有错误代码，添加到错误信息中
            if (result.code) {
                errorMessage += ` (错误代码: ${result.code})`;
            }

            // 添加状态码信息
            errorMessage += ` [HTTP ${response.status}]`;

            showMessage(errorMessage, 'error');

            // 如果是认证相关错误，建议重新登录
            if (response.status === 401 || response.status === 403) {
                setTimeout(() => {
                    if (confirm('认证失败，是否重新登录？')) {
                        localStorage.clear();
                        window.location.href = '/';
                    }
                }, 2000);
            }
        }
    } catch (error) {
        console.error('添加学校网络错误:', error);
        showMessage('网络错误，请重试', 'error');
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

/**
 * 显示编辑学校模态框
 */
function showEditSchoolModal(id, name) {
    currentSchoolId = id;
    document.getElementById('editSchoolName').value = name;
    const modal = new bootstrap.Modal(document.getElementById('editSchoolModal'));
    modal.show();
}

/**
 * 提交编辑学校
 */
async function submitEditSchool() {
    const schoolName = document.getElementById('editSchoolName').value.trim();

    if (!schoolName) {
        showMessage('请输入学校名称', 'error');
        return;
    }

    // 获取可选字段的值
    const schoolAddress = document.getElementById('editSchoolAddress') ?
        document.getElementById('editSchoolAddress').value.trim() : '';
    const schoolPhone = document.getElementById('editSchoolPhone') ?
        document.getElementById('editSchoolPhone').value.trim() : '';
    const schoolEmail = document.getElementById('editSchoolEmail') ?
        document.getElementById('editSchoolEmail').value.trim() : '';

    try {
        const schoolData = {
            name: schoolName
        };

        // 只有非空值才添加到请求中
        if (schoolAddress) schoolData.address = schoolAddress;
        if (schoolPhone) schoolData.contact_phone = schoolPhone;
        if (schoolEmail) schoolData.contact_email = schoolEmail;

        const response = await fetch(`/api/teacher/schools/${currentSchoolId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(schoolData)
        });

        const result = await response.json();

        if (response.ok) {
            showMessage('学校信息更新成功', 'success');

            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('editSchoolModal')).hide();

            // 注意：不需要手动更新界面，Supabase实时订阅会自动处理

        } else {
            showMessage(result.message || result.error || '更新学校信息失败', 'error');
        }
    } catch (error) {
        console.error('更新学校失败:', error);
        showMessage('更新学校信息失败，请重试', 'error');
    }
}

/**
 * 显示删除学校确认模态框
 */
function showDeleteSchoolModal(id, name) {
    currentSchoolId = id;
    document.getElementById('deleteSchoolMessage').textContent =
        `确定要删除学校"${name}"吗？此操作不可恢复。`;
    const modal = new bootstrap.Modal(document.getElementById('deleteSchoolModal'));
    modal.show();
}

/**
 * 确认删除学校
 */
async function confirmDeleteSchool() {
    try {
        const response = await fetch(`/api/teacher/schools/${currentSchoolId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            showMessage('学校删除成功', 'success');

            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('deleteSchoolModal')).hide();

            // 注意：不需要手动更新界面，Supabase实时订阅会自动处理

        } else {
            showMessage(result.message || '删除学校失败', 'error');
        }
    } catch (error) {
        console.error('删除学校失败:', error);
        showMessage('删除学校失败，请重试', 'error');
    }
}

/**
 * 立即更新学校管理页面显示（不重新加载数据）
 */
function updateSchoolManagementDisplay() {
    console.log('立即更新学校管理页面显示...');

    // 如果当前在学校管理页面，更新相关显示元素
    if (currentSection === 'school-management') {
        // 更新学校数量显示
        const schoolCountElement = document.querySelector('h5 i.fas.fa-list').parentElement;
        if (schoolCountElement) {
            schoolCountElement.innerHTML = `<i class="fas fa-list"></i> 学校列表 (${teacherSchools ? teacherSchools.length : 0}所)`;
        }

        // 更新表格内容
        const tableBody = document.querySelector('.table tbody');
        if (tableBody) {
            tableBody.innerHTML = renderSchoolTableRows();
            console.log('学校表格已更新');
        }
    }

    // 更新控制台统计数据（如果在控制台页面）
    if (currentSection === 'dashboard') {
        updateDashboardStats();
    }

    console.log('学校管理页面显示更新完成');
}

/**
 * 更新控制台统计数据
 */
function updateDashboardStats() {
    console.log('更新控制台统计数据...');

    if (currentSection !== 'dashboard') {
        return;
    }

    // 更新学校数量统计
    const schoolCountElement = document.querySelector('.stat-card .stat-number');
    if (schoolCountElement && schoolCountElement.parentElement.querySelector('.stat-label').textContent.includes('学校')) {
        schoolCountElement.textContent = teacherSchools ? teacherSchools.length : 0;
    }

    // 更新学生数量统计
    const studentCountElement = document.querySelectorAll('.stat-card .stat-number')[1];
    if (studentCountElement) {
        studentCountElement.textContent = teacherStudents ? teacherStudents.length : 0;
    }

    // 更新班级数量统计
    const classCountElement = document.querySelectorAll('.stat-card .stat-number')[2];
    if (classCountElement) {
        classCountElement.textContent = teacherClasses ? teacherClasses.length : 0;
    }

    console.log('控制台统计数据更新完成');
}

/**
 * 刷新学校列表
 */
async function refreshSchoolsList() {
    console.log('刷新学校列表...');
    try {
        // 显示刷新状态
        const refreshBtn = document.querySelector('button[onclick="refreshSchoolsList()"]');
        if (refreshBtn) {
            const originalContent = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
            refreshBtn.disabled = true;

            // 恢复按钮状态的函数
            const restoreButton = () => {
                refreshBtn.innerHTML = originalContent;
                refreshBtn.disabled = false;
            };

            setTimeout(restoreButton, 2000); // 2秒后恢复按钮状态
        }

        // 重新加载数据
        const freshSchools = await loadTeacherSchoolsFromAPI();
        teacherSchools = freshSchools;
        console.log('重新加载的学校数据:', teacherSchools);

        // 立即更新显示
        updateSchoolManagementDisplay();

        // 如果当前在学校管理页面，可选择性地重新渲染整个页面（用于复杂更新）
        // showSchoolManagement();

        console.log('学校列表刷新完成');
        showMessage('学校列表已刷新', 'success');
    } catch (error) {
        console.error('刷新学校列表失败:', error);
        showMessage('刷新学校列表失败', 'error');
    }
}

// ==================== 教师学生管理CRUD操作 ====================

/**
 * 显示添加学生表单
 */
function showAddTeacherStudentForm() {
    if (teacherSchools.length === 0) {
        Utils.showMessage('请先添加学校信息', 'warning');
        return;
    }

    const name = prompt('请输入学生姓名:');
    if (!name || !name.trim()) {
        return;
    }

    const studentId = prompt('请输入学号:') || '';
    const grade = prompt('请输入年级 (1-12):');
    const className = prompt('请输入班级:');

    if (!grade || !className) {
        Utils.showMessage('年级和班级不能为空', 'error');
        return;
    }

    // 简化版：使用第一个学校
    const schoolId = teacherSchools[0].id;

    addTeacherStudent(name.trim(), studentId.trim(), schoolId, grade, className);
}

/**
 * 添加教师学生
 */
async function addTeacherStudent(name, studentId, schoolId, grade, className) {
    try {
        const response = await fetch('/api/teacher/students', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                name,
                student_id: studentId,
                school_id: schoolId,
                grade: parseInt(grade),
                class: parseInt(className)
            })
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('学生添加成功', 'success');
            // 重新加载学生数据并刷新列表
            teacherStudents = await loadTeacherStudentsFromAPI(true);
            showTeacherSection('students');
        } else {
            Utils.showMessage(result.message || '添加学生失败', 'error');
        }
    } catch (error) {
        console.error('添加学生失败:', error);
        Utils.showMessage('添加学生失败，请重试', 'error');
    }
}

/**
 * 提交添加学生表单
 */
async function submitAddStudent() {
    // 获取表单数据
    const name = document.getElementById('studentName').value.trim();
    const studentId = document.getElementById('studentId').value.trim();
    const schoolId = document.getElementById('studentSchool').value;
    const grade = document.getElementById('studentGrade').value;
    const studentClass = document.getElementById('studentClass').value;
    const gender = document.getElementById('studentGender').value;
    const groupNumber = document.getElementById('studentGroup').value;

    // 验证必填字段
    if (!name) {
        showMessage('请输入学生姓名', 'error');
        return;
    }
    if (!schoolId) {
        showMessage('请选择学校', 'error');
        return;
    }
    if (!grade) {
        showMessage('请选择年级', 'error');
        return;
    }
    if (!studentClass) {
        showMessage('请选择班级', 'error');
        return;
    }

    try {
        // 准备请求数据
        const requestData = {
            name,
            school_id: parseInt(schoolId),
            grade: parseInt(grade),
            class: parseInt(studentClass)
        };

        // 添加可选字段
        if (studentId) {
            requestData.student_id = studentId;
        }
        if (gender) {
            requestData.gender = gender;
        }
        if (groupNumber && !isNaN(parseInt(groupNumber))) {
            requestData.group_number = parseInt(groupNumber);
        }

        console.log('提交学生数据:', requestData);

        const response = await fetch('/api/teacher/students', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();

        if (response.ok && result.success) {
            console.log('学生添加成功:', result);

            // 关闭模态框
            const modalInstance = bootstrap.Modal.getInstance(document.getElementById('addStudentModal'));
            if (modalInstance) {
                modalInstance.hide();
            }

            // 清空表单
            document.getElementById('studentName').value = '';
            document.getElementById('studentId').value = '';
            document.getElementById('studentSchool').value = '';
            document.getElementById('studentGrade').value = '';
            document.getElementById('studentClass').value = '';
            document.getElementById('studentGender').value = '';
            document.getElementById('studentGroup').value = '';

            // 静默刷新学生列表，避免空白页面
            const updatedStudents = await refreshStudentDataSilently();

            if (updatedStudents) {
                showMessage(`学生"${requestData.name}"添加成功，列表已更新`, 'success');
            } else {
                showMessage('学生添加成功，但列表刷新失败，请手动刷新', 'warning');
            }
        } else {
            console.error('添加学生失败:', result);
            showMessage(result.message || '添加学生失败', 'error');
        }
    } catch (error) {
        console.error('添加学生失败:', error);
        showMessage('添加学生失败，请重试', 'error');
    }
}

/**
 * 编辑教师学生
 */
async function editTeacherStudent(id) {
    const student = teacherStudents.find(s => s.id == id);
    if (!student) {
        Utils.showMessage('学生信息不存在', 'error');
        return;
    }

    const name = prompt('请输入学生姓名:', student.name || student.student_name);
    if (!name || !name.trim()) {
        return;
    }

    const studentId = prompt('请输入学号:', student.student_id || '') || '';
    const grade = prompt('请输入年级:', student.grade || student.student_grade);
    const className = prompt('请输入班级:', student.class || student.student_class);

    if (!grade || !className) {
        Utils.showMessage('年级和班级不能为空', 'error');
        return;
    }

    try {
        const response = await fetch(`/api/teacher/students/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                name: name.trim(),
                student_id: studentId.trim(),
                grade: parseInt(grade),
                class: parseInt(className)
            })
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('学生信息更新成功', 'success');
            // 重新加载学生数据并刷新列表
            teacherStudents = await loadTeacherStudentsFromAPI(true);
            showTeacherSection('students');
        } else {
            Utils.showMessage(result.message || '更新学生信息失败', 'error');
        }
    } catch (error) {
        console.error('更新学生失败:', error);
        Utils.showMessage('更新学生信息失败，请重试', 'error');
    }
}

/**
 * 删除教师学生
 */
async function deleteTeacherStudent(id, name) {
    if (!confirm(`确定要删除学生 "${name}" 吗？此操作不可恢复。`)) {
        return;
    }

    try {
        const response = await fetch(`/api/teacher/students/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('学生删除成功', 'success');
            // 重新加载学生数据并刷新列表
            teacherStudents = await loadTeacherStudentsFromAPI(true);
            showTeacherSection('students');
        } else {
            Utils.showMessage(result.message || '删除学生失败', 'error');
        }
    } catch (error) {
        console.error('删除学生失败:', error);
        Utils.showMessage('删除学生失败，请重试', 'error');
    }
}

// ==================== 工具函数 ====================

/**
 * 检查Utils是否可用，如果不可用则提供简单实现
 */
if (typeof Utils === 'undefined') {
    window.Utils = {
        showMessage: function(message, type) {
            console.log(`[${type.toUpperCase()}] ${message}`);
            alert(message);
        }
    };
}

// ==================== 分级管理功能模块 ====================

/**
 * 学校管理模块
 */
async function showSchoolManagement() {
    console.log('=== 显示学校管理页面 ===');

    // 确保学校数据已加载
    if (!teacherSchools || teacherSchools.length === 0) {
        console.log('重新加载学校数据...');
        try {
            teacherSchools = await loadTeacherSchoolsFromAPI();
        } catch (error) {
            console.error('加载学校数据失败:', error);
            teacherSchools = [];
        }
    }

    console.log('当前学校数据:', teacherSchools);

    const contentArea = document.getElementById('teacher-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-school"></i> 学校管理
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">管理您任教的学校信息</p>
            </div>
            <button onclick="showAddSchoolModal()" class="btn btn-primary">
                <i class="fas fa-plus"></i> 添加学校
            </button>
        </div>

        <!-- 学校管理操作指南 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info border-0 shadow-sm">
                    <h6><i class="fas fa-info-circle"></i> 操作指南</h6>
                    <p class="mb-2">1. <strong>添加学校</strong>：点击"添加学校"按钮创建新的学校信息</p>
                    <p class="mb-2">2. <strong>管理学校</strong>：在学校列表中可以编辑或删除学校信息</p>
                    <p class="mb-0">3. <strong>下一步</strong>：完成学校添加后，请前往"年级管理"为学校配置年级和班级</p>
                </div>
            </div>
        </div>

        <!-- 学校列表 -->
        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5><i class="fas fa-list"></i> 学校列表 (${teacherSchools ? teacherSchools.length : 0}所)</h5>
                <div class="btn-group">
                    <button onclick="refreshSchoolsList()"
                            class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead style="background: #f8f9fa;">
                        <tr>
                            <th style="padding: 15px; font-weight: 600; color: #495057; border: none;">
                                <i class="fas fa-school text-primary"></i> 学校名称
                            </th>
                            <th style="padding: 15px; font-weight: 600; color: #495057; border: none;">联系信息</th>
                            <th style="padding: 15px; font-weight: 600; color: #495057; border: none;">创建时间</th>
                            <th style="padding: 15px; font-weight: 600; color: #495057; border: none; text-align: center;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${renderSchoolTableRows()}
                    </tbody>
                </table>
            </div>
        </div>

        ${renderSchoolModals()}
    `;
}

/**
 * 渲染学校表格行
 */
function renderSchoolTableRows() {
    if (!teacherSchools || teacherSchools.length === 0) {
        return `
            <tr>
                <td colspan="4" style="padding: 40px; text-align: center; color: #666;">
                    <div>
                        <i class="fas fa-school fa-3x mb-3" style="opacity: 0.3;"></i>
                        <h6>暂无学校数据</h6>
                        <p class="mb-3">您还没有添加任何学校信息</p>
                        <button onclick="showAddSchoolModal()" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 立即添加学校
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    return teacherSchools.map(school => `
        <tr style="transition: background-color 0.2s;">
            <td style="padding: 15px; border: none; vertical-align: middle;">
                <div class="d-flex align-items-center">
                    <div class="school-avatar" style="width: 40px; height: 40px; border-radius: 8px; background: linear-gradient(135deg, #28a745, #20c997); display: flex; align-items: center; justify-content: center; color: white; margin-right: 12px; font-weight: bold;">
                        ${school.name ? school.name.charAt(0) : 'S'}
                    </div>
                    <div>
                        <strong style="color: #333; font-size: 16px;">${school.name || '未知学校'}</strong>
                        <div style="font-size: 12px; color: #666; margin-top: 2px;">
                            ${school.address ? `地址：${school.address}` : '暂无地址信息'}
                        </div>
                    </div>
                </div>
            </td>
            <td style="padding: 15px; border: none; vertical-align: middle;">
                <div style="font-size: 14px;">
                    ${school.contact_phone ? `<div><i class="fas fa-phone text-success"></i> ${school.contact_phone}</div>` : ''}
                    ${school.contact_email ? `<div><i class="fas fa-envelope text-primary"></i> ${school.contact_email}</div>` : ''}
                    ${!school.contact_phone && !school.contact_email ? '<span class="text-muted">暂无联系信息</span>' : ''}
                </div>
            </td>
            <td style="padding: 15px; border: none; vertical-align: middle;">
                <span class="badge bg-light text-dark">${formatDateTime(school.created_at) || '未知'}</span>
            </td>
            <td style="padding: 15px; border: none; vertical-align: middle; text-align: center;">
                <div class="btn-group">
                    <button onclick="showEditSchoolModal('${school.id}', '${school.name || ''}')"
                            class="btn btn-sm btn-outline-success" title="编辑学校">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="viewSchoolDetails('${school.id}')"
                            class="btn btn-sm btn-outline-primary" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="showDeleteSchoolModal('${school.id}', '${school.name || '未知学校'}')"
                            class="btn btn-sm btn-outline-danger" title="删除学校">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * 渲染学校相关模态框
 */
function renderSchoolModals() {
    return `
        <!-- 添加学校模态框 -->
        <div class="modal fade" id="addSchoolModal" tabindex="-1" data-bs-backdrop="true" data-bs-keyboard="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title"><i class="fas fa-school"></i> 添加学校</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addSchoolForm">
                            <div class="mb-3">
                                <label for="schoolName" class="form-label">学校名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="schoolName" required placeholder="请输入学校全名" autocomplete="off">
                                <div class="form-text">请输入完整的学校名称，这是唯一必填项</div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="button" class="btn btn-success" onclick="submitAddSchool()">
                            <i class="fas fa-check"></i> 确定添加
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑学校模态框 -->
        <div class="modal fade" id="editSchoolModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title"><i class="fas fa-edit"></i> 编辑学校信息</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editSchoolForm">
                            <div class="mb-3">
                                <label for="editSchoolName" class="form-label">学校名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="editSchoolName" required>
                            </div>
                            <div class="mb-3">
                                <label for="editSchoolAddress" class="form-label">学校地址</label>
                                <textarea class="form-control" id="editSchoolAddress" rows="2"></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editSchoolPhone" class="form-label">联系电话</label>
                                        <input type="tel" class="form-control" id="editSchoolPhone">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editSchoolEmail" class="form-label">联系邮箱</label>
                                        <input type="email" class="form-control" id="editSchoolEmail">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="submitEditSchool()">
                            <i class="fas fa-save"></i> 保存修改
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 删除确认模态框 -->
        <div class="modal fade" id="deleteSchoolModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title"><i class="fas fa-exclamation-triangle"></i> 确认删除</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center py-3">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <p id="deleteSchoolMessage" class="h6"></p>
                            <div class="alert alert-warning">
                                <small><i class="fas fa-info-circle"></i> 此操作不可恢复，请谨慎操作！</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="button" class="btn btn-danger" onclick="confirmDeleteSchool()">
                            <i class="fas fa-trash"></i> 确定删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 年级管理模块
 */
async function showGradeManagement() {
    // 确保学校数据是最新的，但避免重复加载
    if (!teacherSchools || teacherSchools.length === 0) {
        console.log('年级管理：重新加载学校数据...');
        try {
            teacherSchools = await loadTeacherSchoolsFromAPI(true);
            console.log('年级管理：学校数据已更新，共', teacherSchools.length, '个学校');
        } catch (error) {
            console.error('年级管理：加载学校数据失败:', error);
            teacherSchools = [];
        }
    } else {
        console.log('年级管理：使用缓存的学校数据，共', teacherSchools.length, '个学校');
    }

    const contentArea = document.getElementById('teacher-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-layer-group"></i> 年级管理（任教设置）
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">参考管理员界面，选择“我在每个学校下各年级的任教班级”。年级总班级数仅由管理员维护。</p>
            </div>
            <div>
                <button onclick="refreshGradeManagementData()" class="btn btn-outline-secondary btn-sm" title="刷新学校和年级数据">
                    <i class="fas fa-sync-alt"></i> 刷新数据
                </button>
            </div>
        </div>

        ${teacherSchools.length === 0 ? `
        <div class="text-center py-5">
            <i class="fas fa-school fa-4x text-muted mb-4"></i>
            <h4 class="text-muted">还没有学校信息</h4>
            <p class="text-muted">请先添加学校信息，然后再进行任教班级设置</p>
            <button onclick="showTeacherSection('school-management')" class="btn btn-success">
                <i class="fas fa-school"></i> 前往学校管理
            </button>
        </div>

            <!-- 仅渲染本人可见学校：teacherSchools ∩ teacherClasses.school_id -->

        ` : `
            <div id="teacher-assignment-container">
                ${(getVisibleSchools(teacherSchools, teacherClasses)).map(s => `
                    <div class="school-assignment-section mb-4" data-school-id="${s.id}">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-school me-2"></i>${s.name}</h6>
                                <div>
                                    <button class="btn btn-sm btn-outline-light" onclick="window.saveTeacherClassesForSchool && window.saveTeacherClassesForSchool(${s.id})">保存本校任教班级</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row" id="grades-container-${s.id}">
                                    <!-- 年级配置将在这里动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `}
    `;

    // 为每个学校并行加载年级配置，提高性能
    const loadGradePromises = (teacherSchools || []).map(async (school) => {
            const container = document.getElementById(`grades-container-${school.id}`);

            // 检查缓存
            const cacheKey = `grades_${school.id}`;
            const cachedGrades = getCache(cacheKey);

            if (cachedGrades && !window.forceRefreshGrades) {
                console.log(`使用缓存的年级数据: ${school.name}`);
                const html = renderTeacherGradeClassSelection(school.id, cachedGrades);
                if (container) container.innerHTML = html;
                await precheckTeacherClassesForSchool(school.id);
                return;
            }

            // 显示友好的加载状态
            if (container) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="text-muted mt-2 mb-0">正在加载年级配置...</p>
                    </div>
                `;
            }

            try {
                console.log(`加载年级配置: ${school.name}`);

                // 添加重试机制
                let resp;
                let lastError;
                const maxRetries = 2;

                for (let attempt = 1; attempt <= maxRetries; attempt++) {
                    try {
                        console.log(`尝试加载年级配置 (${attempt}/${maxRetries}): ${school.name}`);
                        resp = await authFetch(`/api/teacher/schools/${school.id}/grades`, {}, 15000); // Vercel环境增加到15秒
                        break; // 成功则跳出重试循环
                    } catch (error) {
                        lastError = error;
                        console.warn(`第${attempt}次尝试失败: ${school.name} - ${error.message}`);

                        if (attempt < maxRetries) {
                            // 等待1秒后重试
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                    }
                }

                if (!resp) {
                    throw lastError || new Error('所有重试都失败了');
                }
                let cfgs = [];

                if (resp.ok) {
                    try {
                        const result = await resp.json();
                        cfgs = (result && (result.success === undefined || result.success === true))
                            ? (Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []))
                            : [];
                    } catch (parseError) {
                        console.warn(`解析年级数据失败: ${school.name}`, parseError);
                        cfgs = [];
                    }
                } else {
                    // 处理HTTP错误
                    const errorText = await resp.text().catch(() => '未知错误');
                    console.error(`年级配置API错误: ${school.name} - ${resp.status} ${errorText}`);
                    cfgs = [];
                }

                // 缓存年级配置数据（即使是空数组也缓存，避免重复请求）
                setCache(cacheKey, cfgs);

                const html = renderTeacherGradeClassSelection(school.id, cfgs);
                if (container) container.innerHTML = html;

                // 异步预选任教班级，不阻塞界面
                precheckTeacherClassesForSchool(school.id).catch(err => {
                    console.warn(`预选任教班级失败: ${school.name}`, err);
                });
            } catch (e) {
                console.error('加载学校年级配置失败:', e);

                // 缓存空数据，避免重复失败的请求
                setCache(cacheKey, []);

                if (container) {
                    container.innerHTML = `
                        <div class="alert alert-warning">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                <strong>${school.name}</strong> 年级配置加载失败
                            </div>
                            <small class="text-muted">错误: ${e.message}</small>
                            <div class="mt-3">
                                <button class="btn btn-sm btn-outline-primary me-2" onclick="retryLoadGrades(${school.id})">
                                    <i class="fas fa-redo"></i> 重试加载
                                </button>
                                <button class="btn btn-sm btn-primary me-2" onclick="showAddGradeModal(${school.id})">
                                    <i class="fas fa-plus"></i> 添加年级
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="useOfflineMode(${school.id})">
                                    <i class="fas fa-tools"></i> 离线模式
                                </button>
                            </div>
                        </div>
                    `;
                }
            }
        });

    // 等待所有年级配置加载完成
    Promise.allSettled(loadGradePromises).then(results => {
        const failed = results.filter(r => r.status === 'rejected').length;
        const total = results.length;
        console.log(`年级配置加载完成: ${total - failed}/${total} 成功`);

        if (failed > 0) {
            console.warn(`${failed} 个学校的年级配置加载失败`);
        }

        // 重置强制刷新标志
        window.forceRefreshGrades = false;
    });
}

// 刷新年级管理数据
async function refreshGradeManagementData() {
    try {
        showMessage('正在刷新学校和年级数据...', 'info');

        // 清除年级配置缓存
        teacherSchools.forEach(school => {
            clearCache(`grades_${school.id}`);
        });

        // 设置强制刷新标志
        window.forceRefreshGrades = true;

        // 强制重新加载学校数据
        teacherSchools = await loadTeacherSchoolsFromAPI(true);

        // 重新显示年级管理页面
        await showGradeManagement();

        showMessage(`数据已刷新，共 ${teacherSchools.length} 个学校`, 'success');
    } catch (error) {
        console.error('刷新年级管理数据失败:', error);
        showMessage('刷新数据失败，请重试', 'error');
    }
}

// 生成仅任教班级选择（参考管理员界面）
function renderTeacherGradeClassSelection(schoolId, configs) {
    if (!Array.isArray(configs) || !configs.length) {
        return `
            <div class="text-center py-4">
                <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">该学校暂无年级配置</h6>
                <p class="text-muted mb-3">请先添加年级，然后设置班级数量和任教班级</p>
                <button class="btn btn-primary btn-sm" onclick="showAddGradeModal(${schoolId})">
                    <i class="fas fa-plus"></i> 添加年级
                </button>
            </div>
        `;
    }
    return configs.map(cfg => {
        const grade = parseInt(cfg.grade);
        const total = parseInt(cfg.class_count) || 0;
        const items = Array.from({length: total}, (_, i) => i + 1).map(cls => `
            <div class=\"form-check form-check-inline\">
              <input class=\"form-check-input t-assign\" type=\"checkbox\" id=\"ta-${schoolId}-${grade}-${cls}\" data-school-id=\"${schoolId}\" data-grade=\"${grade}\" data-class=\"${cls}\">
              <label class=\"form-check-label\" for=\"ta-${schoolId}-${grade}-${cls}\">${cls}班</label>
            </div>
        `).join('') || '<div class=\"text-muted\">未配置班级</div>';
        return `
          <div class=\"col-md-6 col-lg-4 mb-3\">
            <div class=\"card border-secondary\">
              <div class=\"card-header bg-light d-flex justify-content-between align-items-center\">
                <h6 class=\"mb-0\">${grade}年级</h6>
                <div class=\"btn-group\">
                  <button class=\"btn btn-sm btn-outline-primary\" onclick=\"selectAllTeacherClasses(${schoolId}, ${grade})\">全选</button>
                  <button class=\"btn btn-sm btn-outline-secondary\" onclick=\"clearAllTeacherClasses(${schoolId}, ${grade})\">清空</button>
                  <button class=\"btn btn-sm btn-outline-success\" onclick=\"window.openSetClassCountModal(${schoolId}, ${grade}, ${total})\">设置班级数量</button>
                </div>
              </div>
              <div class=\"card-body p-2\">${items}</div>
            </div>
          </div>`;
    }).join('') + `
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-dashed border-primary" style="border-style: dashed !important; border-width: 2px;">
                <div class="card-body text-center py-4">
                    <i class="fas fa-plus fa-2x text-primary mb-2"></i>
                    <h6 class="text-primary mb-2">添加年级</h6>
                    <button class="btn btn-outline-primary btn-sm" onclick="showAddGradeModal(${schoolId})">
                        <i class="fas fa-plus"></i> 添加
                    </button>
                </div>
            </div>
        </div>
    `;
}

// 显示添加年级模态框
function showAddGradeModal(schoolId) {
    const school = teacherSchools.find(s => s.id == schoolId);
    if (!school) {
        showMessage('学校信息不存在', 'error');
        return;
    }

    const modalId = 'addGradeModal';
    const existing = document.getElementById(modalId);
    if (existing) existing.remove();

    const modalHtml = `
        <div class="modal fade" id="${modalId}" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-plus"></i> 添加年级 - ${school.name}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addGradeForm">
                            <div class="mb-3">
                                <label for="gradeNumber" class="form-label">年级 <span class="text-danger">*</span></label>
                                <select class="form-select" id="gradeNumber" required>
                                    <option value="">请选择年级</option>
                                    <option value="1">一年级</option>
                                    <option value="2">二年级</option>
                                    <option value="3">三年级</option>
                                    <option value="4">四年级</option>
                                    <option value="5">五年级</option>
                                    <option value="6">六年级</option>
                                    <option value="7">七年级</option>
                                    <option value="8">八年级</option>
                                    <option value="9">九年级</option>
                                    <option value="10">高一</option>
                                    <option value="11">高二</option>
                                    <option value="12">高三</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="classCount" class="form-label">班级数量 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="classCount" min="1" max="20" value="1" required>
                                <div class="form-text">设置该年级的班级总数（1-20个班级）</div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="confirmAddGrade(${schoolId})">
                            <i class="fas fa-plus"></i> 添加年级
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();
}

// 确认添加年级
async function confirmAddGrade(schoolId) {
    const gradeNumber = document.getElementById('gradeNumber').value;
    const classCount = document.getElementById('classCount').value;

    if (!gradeNumber || !classCount) {
        showMessage('请填写完整的年级信息', 'error');
        return;
    }

    try {
        const response = await authFetch('/api/teacher/grades', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                school_id: schoolId,
                grade_number: parseInt(gradeNumber),
                class_count: parseInt(classCount)
            })
        });

        if (response.ok) {
            showMessage(`年级添加成功！${gradeNumber}年级已设置${classCount}个班级`, 'success');

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addGradeModal'));
            if (modal) modal.hide();

            // 清除缓存并刷新年级数据
            clearCache(`grades_${schoolId}`);

            // 重新加载该学校的年级配置
            const container = document.getElementById(`grades-container-${schoolId}`);
            if (container) {
                container.innerHTML = '<div class="text-muted">正在刷新年级配置...</div>';

                try {
                    const resp = await authFetch(`/api/teacher/schools/${schoolId}/grades`, {}, 2000);
                    const result = await resp.json();
                    const cfgs = (resp.ok && result && (result.success === undefined || result.success === true))
                        ? (Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []))
                        : [];

                    // 更新缓存
                    setCache(`grades_${schoolId}`, cfgs);

                    // 重新渲染
                    const html = renderTeacherGradeClassSelection(schoolId, cfgs);
                    container.innerHTML = html;

                    // 预选任教班级
                    await precheckTeacherClassesForSchool(schoolId);
                } catch (error) {
                    console.error('刷新年级配置失败:', error);
                    container.innerHTML = '<div class="text-danger">刷新失败，请手动刷新页面</div>';
                }
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || '添加年级失败');
        }
    } catch (error) {
        console.error('添加年级失败:', error);
        showMessage('添加年级失败: ' + error.message, 'error');
    }
}
// 打开“设置班级数量”模态框（数字输入）
function openSetClassCountModal(schoolId, grade, currentTotal) {
  const id = 'setClassCountModal';
  const existing = document.getElementById(id);
  if (existing) existing.remove();
  const html = `
  <div class="modal fade" id="${id}" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-success text-white">
          <h5 class="modal-title"><i class="fas fa-cogs"></i> 设置班级数量 - ${grade}年级</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <label class="form-label">班级数量（1-99）</label>
          <input type="number" min="1" max="99" id="classCountInput" class="form-control" value="${Math.max(1, parseInt(currentTotal)||1)}">
          <div class="form-text">保存后将写入 school_grade_configs.class_count</div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-success" id="saveClassCountBtn">保存</button>
        </div>
      </div>
    </div>
  </div>`;
  document.body.insertAdjacentHTML('beforeend', html);
  const modalEl = document.getElementById(id);
  const modal = new bootstrap.Modal(modalEl);
  modal.show();
  document.getElementById('saveClassCountBtn').onclick = async () => {
    const v = parseInt(document.getElementById('classCountInput').value);
    if (!v || v < 1 || v > 99) {
      showMessage('请输入1-99之间的有效数字', 'error');
      return;
    }
    try {
      const resp = await authFetch(`/api/teacher/schools/${schoolId}/grades/${grade}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${localStorage.getItem('token')}` },
        body: JSON.stringify({ class_count: v })
      });
      const result = await resp.json();
      if (!resp.ok || !result.success) throw new Error(result.message || '保存失败');
      showMessage('班级数量已更新', 'success');
      modal.hide();
      // 局部刷新该学校的年级勾选
      try {
        const r = await fetchWithTimeout(`/api/teacher/schools/${schoolId}/grades`, {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }, 5000);
        const jr = await r.json();
        const cfgs = (r.ok && jr && (jr.success === undefined || jr.success === true)) ? (jr.data || []) : [];
        const container = document.getElementById(`grades-container-${schoolId}`);
        if (container) {
          container.innerHTML = renderTeacherGradeClassSelection(schoolId, cfgs);
          await precheckTeacherClassesForSchool(schoolId);
        }
      } catch (e) {
        console.warn('局部刷新失败:', e);
      }
    } catch (e) {
      console.error(e);
      showMessage('保存失败，请重试', 'error');
    }
  };
}

window.openSetClassCountModal = openSetClassCountModal;

async function precheckTeacherClassesForSchool(schoolId) {
    try {
        const resp = await fetch(`/api/teacher/classes/permissions?school_id=${schoolId}`, {
            headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        });
        const json = await resp.json();
        const list = (resp.ok && json.success) ? (json.data || []) : [];
        list.forEach(p => {
            const id = `ta-${p.school_id}-${p.grade}-${p.class}`;
            const el = document.getElementById(id);
            if (el) el.checked = true;
        });
    } catch (e) {
        console.warn('预勾选任教班级失败:', e);
    }
}

function selectAllTeacherClasses(schoolId, grade) {
    document.querySelectorAll(`input.t-assign[data-school-id="${schoolId}"][data-grade="${grade}"]`).forEach(cb => cb.checked = true);
}
function clearAllTeacherClasses(schoolId, grade) {
    document.querySelectorAll(`input.t-assign[data-school-id="${schoolId}"][data-grade="${grade}"]`).forEach(cb => cb.checked = false);
}

async function saveTeacherClassesForSchool(schoolId) {
    try {
        const checked = Array.from(document.querySelectorAll(`input.t-assign[data-school-id="${schoolId}"]:checked`)).map(chk => ({
            school_id: parseInt(chk.getAttribute('data-school-id')),
            grade: parseInt(chk.getAttribute('data-grade')),
            class: parseInt(chk.getAttribute('data-class'))
        }));
        const resp = await fetch('/api/teacher/classes/permissions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${localStorage.getItem('token')}` },
            body: JSON.stringify({ classes: checked, school_id: parseInt(schoolId) })
        });
        const result = await resp.json();
        if (!resp.ok || !result.success) throw new Error(result.message || '保存失败');
        showMessage('本校任教班级已保存', 'success');
        // 刷新“我的任教班级”统计
        teacherClasses = await loadTeacherClassesFromAPI();
        if (currentSection === 'dashboard') showTeacherDashboard();
    } catch (e) {
        console.error('保存任教班级失败:', e);
        showMessage('保存任教班级失败，请重试', 'error');
    }
}

// 全局导出供按钮调用
window.saveTeacherClassesForSchool = saveTeacherClassesForSchool;
window.selectAllTeacherClasses = selectAllTeacherClasses;
window.clearAllTeacherClasses = clearAllTeacherClasses;

// 旧版按学校年级卡片与旧模态相关代码已清理

// 班级管理功能已删除，班级配置现在在年级管理中完成

/**
 * 学生管理模块
 */
async function showStudentManagement() {
    const contentArea = document.getElementById('teacher-content-area');

    // 先显示基本结构，避免白屏
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-users"></i> 学生管理
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">管理学生信息，支持单个添加和Excel批量导入</p>
            </div>
            <div class="d-flex gap-2">
                <div class="btn-group">
                    <button onclick="showAddStudentModal()" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> 添加学生
                    </button>
                    <button id="btnExcelImport" class="btn btn-primary">
                        <i class="fas fa-file-excel"></i> Excel导入
                    </button>
                    <button id="btnDownloadTemplate" class="btn btn-outline-secondary">
                        <i class="fas fa-download"></i> 下载模板
                    </button>
                </div>
            </div>
        </div>

        <!-- 导入说明 -->
        <div class="row mb-4">
            <script>
              (function(){
                var btn = document.getElementById('btnDownloadTemplate');
                if (btn) {
                  btn.addEventListener('click', function(){
                    try { if (typeof downloadExcelTemplate === 'function') return downloadExcelTemplate(); } catch(_) {}
                    // 兜底：无函数时直接下载CSV模板
                    try {
                      var csvHeader = '学校名称,年级,班级,学生姓名,学号,性别,备注';
                      var csvRows = [
                        '示例小学,1,1,张三,20240001,男,',
                        '示例小学,1,1,李四,20240002,女,',
                        '示例小学,1,2,王五,20240003,男,',
                        '示例中学,7,1,赵六,20247001,女,'
                      ];
                      var csvContent = [csvHeader].concat(csvRows).join('\n');
                      var blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                      var a = document.createElement('a');
                      var url = URL.createObjectURL(blob);
                      a.href = url;
                      a.download = '学生导入模板_' + new Date().toISOString().slice(0,10) + '.csv';
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);
                    } catch(e) { console.error('下载模板失败:', e); }
                  });
                }
              })();
            </script>
            <div class="col-12">
                <div class="alert alert-info border-0 shadow-sm">
                    <h6><i class="fas fa-info-circle"></i> Excel导入说明</h6>
                    <p class="mb-2">1. <strong>下载模板</strong>：点击"下载模板"获取标准Excel模板</p>
                    <p class="mb-2">2. <strong>填写数据</strong>：模板包含学校、年级、班级、姓名、学号等完整信息</p>
            <script>
              (function(){
                var importBtn = document.getElementById('btnExcelImport');
                if (importBtn) {
                  importBtn.addEventListener('click', function(){
                    if (typeof showExcelImportModal === 'function') {
                      return showExcelImportModal();
                    }
                    // 兜底：直接创建并打开简化导入模态（仅提示加载失败）
                    try {
                      alert('导入组件未加载，请刷新页面后重试');
                    } catch(_) {}
                  });
                }
              })();
            </script>
                    <p class="mb-2">3. <strong>批量导入</strong>：一次性导入多个学校、多个年级班级的学生信息</p>
                    <p class="mb-0">4. <strong>自动创建</strong>：如果学校、年级、班级不存在，系统将自动创建</p>
                </div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label class="form-label">选择学校</label>
                                <select class="form-select" id="filterSchool" onchange="updateFilterGrades()">
                                    <option value="">全部学校</option>
                                    ${teacherSchools.map(school =>
                                        `<option value="${school.id}">${school.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">年级</label>
                                <select class="form-select" id="filterGrade" onchange="updateFilterClasses()">
                                    <option value="">请先选择学校</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">班级</label>
                                <select class="form-select" id="filterClass">
                                    <option value="">请先选择年级</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">搜索学生</label>
                                <input type="text" class="form-control" id="searchStudent" placeholder="输入学生姓名或学号" onkeypress="handleSearchKeyPress(event)">
                            </div>
                            <div class="col-md-2">
                                <div class="btn-group w-100">
                                    <button onclick="updateStudentFilter()" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button onclick="resetStudentFilter()" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 筛选统计信息 -->
                        <div class="row mt-2">
                            <div class="col-12">
                                <div id="filterStats"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学生列表 -->
        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-3"><i class="fas fa-users"></i> 学生列表 (${teacherStudents ? teacherStudents.length : 0}人)</h5>
                    <!-- 批量操作状态显示 -->
                    <div id="batchStatusBadge" class="badge bg-warning text-dark" style="display: none;">
                        已选择 <span id="selectedCountInline">0</span> 个学生
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <!-- 常规操作按钮 -->
                    <div class="btn-group">
                        <button onclick="refreshStudentData()"
                                class="btn btn-outline-secondary btn-sm"
                                title="手动刷新学生列表">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                        <button onclick="exportStudentData()" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>

                    <!-- 批量操作按钮组 -->
                    <div class="btn-group" id="batchOperationButtonsInline" style="display: none;">
                        <button onclick="showBatchEditModal()" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> 批量编辑
                        </button>
                        <button onclick="showBatchTransferModal()" class="btn btn-info btn-sm">
                            <i class="fas fa-exchange-alt"></i> 批量转班
                        </button>
                        <button onclick="batchDeleteStudents()" class="btn btn-danger btn-sm">
                            <i class="fas fa-trash"></i> 批量删除
                        </button>
                        <button onclick="clearSelection()" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i> 取消选择
                        </button>
                    </div>
                </div>
            </div>

            <!-- 批量操作提示 -->
            <div id="batchSelectionInfo" class="alert alert-info" style="display: none;">
                <div class="d-flex justify-content-between align-items-center">
                    <span>已选择 <strong id="selectedCount">0</strong> 个学生</span>
                    <button onclick="clearSelection()" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消选择
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead style="background: #f8f9fa;">
                        <tr>
                            <th style="padding: 15px; font-weight: 600; color: #495057; border: none; width: 50px;">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th style="padding: 15px; font-weight: 600; color: #495057; border: none;">学校</th>
                            <th style="padding: 15px; font-weight: 600; color: #495057; border: none;">班级</th>
                            <th style="padding: 15px; font-weight: 600; color: #495057; border: none;">姓名</th>
                            <th style="padding: 15px; font-weight: 600; color: #495057; border: none; text-align: center;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="student-table-body">
                        ${renderStudentTableRows()}
                    </tbody>
                </table>
            </div>
        </div>



        ${renderEnhancedStudentManagementModals()}
    `;

    // 渲染完成后绑定“下载模板/Excel导入”按钮事件，避免内嵌<script>不执行导致无响应
    (function bindStudentMgmtButtons(){
        try {
            const btnTpl = document.getElementById('btnDownloadTemplate');
            if (btnTpl && !btnTpl.__bound) {
                btnTpl.addEventListener('click', function(){
                    try {
                        var fn = (window && window.downloadExcelTemplate) || downloadExcelTemplate;
                        if (typeof fn === 'function') return fn();
                    } catch(_) {}
                    // 兜底：生成CSV模板
                    try {
                        const csvHeader = '学校名称,年级,班级,学生姓名,学号,性别,组号,备注';
                        const csvRows = [
                            '示例小学,1,1,张三,20240001,男,1,',
                            '示例小学,1,1,李四,20240002,女,1,',
                            '示例小学,1,2,王五,20240003,男,2,',
                            '示例中学,7,1,赵六,20247001,女,,'
                        ];
                        const csvContent = [csvHeader].concat(csvRows).join('\n');
                        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                        const a = document.createElement('a');
                        const url = URL.createObjectURL(blob);
                        a.href = url;
                        a.download = '学生导入模板_' + new Date().toISOString().slice(0,10) + '.csv';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                    } catch(e) { console.error('下载模板失败:', e); }
                });
                btnTpl.__bound = true;
            }

            const btnImp = document.getElementById('btnExcelImport');
            if (btnImp && !btnImp.__bound) {
                btnImp.addEventListener('click', function(){
                    // 直接内联实现导入模态，避免依赖全局函数
                    try {
                        // 移除已存在的模态框
                        const existingModal = document.getElementById('excelImportModal');
                        if (existingModal) existingModal.remove();

                        // 创建导入模态HTML
                        const modalHtml = `
                            <div class="modal fade" id="excelImportModal" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header bg-primary text-white">
                                            <h5 class="modal-title"><i class="fas fa-file-excel"></i> Excel批量导入学生</h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="alert alert-info">
                                                <h6>导入说明</h6>
                                                <p class="mb-2">• 支持导入包含完整学校、年级、班级、学生信息的Excel文件</p>
                                                <p class="mb-2">• 如果学校、年级、班级不存在，系统将自动创建</p>
                                                <p class="mb-0">• 建议先下载模板，按格式填写后再导入</p>
                                            </div>
                                            <div class="file-upload-area border-2 border-dashed border-primary rounded p-4 text-center">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                                <h6>拖拽Excel文件到此处或点击选择文件</h6>
                                                <p class="text-muted mb-3">支持 .xlsx 和 .xls 格式</p>
                                                <input type="file" id="excelFileInput" accept=".xlsx,.xls" style="display: none;">
                                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('excelFileInput').click()">
                                                    <i class="fas fa-folder-open"></i> 选择文件
                                                </button>
                                            </div>
                                            <div id="importPreview" style="display: none;" class="mt-4">
                                                <h6>导入预览</h6>
                                                <div id="previewContent" class="border rounded p-3"></div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                            <button type="button" class="btn btn-primary" id="confirmImportBtn" disabled>
                                                <i class="fas fa-upload"></i> 确认导入
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;

                        // 添加到页面并显示
                        document.body.insertAdjacentHTML('beforeend', modalHtml);

                        // 检查bootstrap是否可用
                        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                            const modal = new bootstrap.Modal(document.getElementById('excelImportModal'));
                            modal.show();
                        } else {
                            // 降级方案：直接显示模态框
                            console.warn('Bootstrap未加载，使用基础模态框显示');
                            const modalElement = document.getElementById('excelImportModal');
                            if (modalElement) {
                                modalElement.style.display = 'block';
                                modalElement.classList.add('show');
                            }
                        }

                        // 绑定文件选择和导入确认事件
                        setupInlineExcelFileUpload();

                    } catch(e) {
                        console.error('打开导入模态失败:', e);
                        alert('打开导入窗口失败：' + e.message);
                    }
                });
                btnImp.__bound = true;
            }
        } catch (e) { console.warn('绑定学生管理按钮失败', e); }
    })();

    // 异步加载数据，避免阻塞界面渲染
    loadStudentDataAsync();

    // 初始化筛选统计信息
    setTimeout(() => {
        updateFilterStats(teacherStudents ? teacherStudents.length : 0, teacherStudents ? teacherStudents.length : 0);
    }, 100);


}

// 异步加载学生数据
async function loadStudentDataAsync(forceReload = false) {
    try {
        // 检查是否需要重新加载数据
        const now = Date.now();
        const lastLoadTime = window.lastStudentDataLoadTime || 0;
        const cacheTimeout = 300000; // 5分钟缓存，减少频繁请求

        // 如果数据已存在且不是强制重载且在缓存时间内，直接更新统计
        if (!forceReload && teacherStudents && teacherStudents.length > 0 && (now - lastLoadTime) < cacheTimeout) {
            console.log('使用缓存的学生数据，避免频繁请求');
            updateFilterStats(teacherStudents.length, teacherStudents.length);
            return;
        }

        // 显示加载状态
        const tbody = document.querySelector('#student-table-body');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" style="padding: 40px; text-align: center;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="text-muted mt-2">正在加载学生数据...</p>
                    </td>
                </tr>
            `;
        }

        try {
            // 并行加载学校和学生数据，增加错误处理
            const [students, schools] = await Promise.all([
                loadTeacherStudentsFromAPI(true).catch(error => {
                    console.error('加载学生数据失败:', error);
                    return []; // 返回空数组而不是抛出错误
                }),
                teacherSchools && teacherSchools.length > 0 ?
                    Promise.resolve(teacherSchools) :
                    loadTeacherSchoolsFromAPI().catch(error => {
                        console.error('加载学校数据失败:', error);
                        return teacherSchools || []; // 使用现有数据或空数组
                    })
            ]);

            // 更新全局数据
            teacherStudents = students || [];
            if (schools) teacherSchools = schools;

            // 记录加载时间
            window.lastStudentDataLoadTime = Date.now();

            // 重新渲染学生表格
            if (tbody) {
                tbody.innerHTML = renderStudentTableRows();
            }

            // 更新统计信息
            updateFilterStats(students.length, students.length);

            // 更新学生数量显示
            const countElement = document.querySelector('h5');
            if (countElement && countElement.textContent.includes('学生列表')) {
                countElement.innerHTML = `<i class="fas fa-users"></i> 学生列表 (${students.length}人)`;
            }

            console.log(`学生数据加载完成: ${students.length} 个学生`);

        } catch (loadError) {
            console.error('学生数据加载过程中出错:', loadError);

            // 显示错误状态
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="padding: 40px; text-align: center;">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                <h6>学生数据加载失败</h6>
                                <p class="text-muted">错误: ${loadError.message}</p>
                                <div class="btn-group">
                                    <button onclick="loadStudentDataAsync(true)" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-redo"></i> 重试加载
                                    </button>
                                    <button onclick="showAddStudentModal()" class="btn btn-success btn-sm">
                                        <i class="fas fa-user-plus"></i> 添加学生
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
            }
        }

        // 初始化批量操作按钮状态
        updateBatchButtons();

    } catch (error) {
        console.error('加载学生数据失败:', error);
        hideLoadingState('students');
    }
}

// 刷新学生数据（强制刷新）
async function refreshStudentData() {
    try {
        console.log('刷新学生数据...');

        // 清除缓存时间戳
        window.lastStudentDataLoadTime = 0;

        // 使用强化的API加载函数
        const students = await loadTeacherStudentsFromAPI(true);

        // 更新全局数据
        teacherStudents = students;

        // 记录刷新时间
        window.lastStudentDataLoadTime = Date.now();

        // 重新渲染表格
        const tbody = document.querySelector('#student-table-body');
        if (tbody) {
            tbody.innerHTML = renderStudentTableRows();
        }

        // 更新统计信息
        updateFilterStats(students.length, students.length);

        // 更新学生数量显示
        const countElement = document.querySelector('h5');
        if (countElement && countElement.textContent.includes('学生列表')) {
            countElement.innerHTML = `<i class="fas fa-users"></i> 学生列表 (${students.length}人)`;
        }

        // 清除选择状态
        clearSelection();

        console.log(`刷新完成，当前显示 ${students.length} 个学生`);
        showMessage(`学生列表已更新 (${students.length}人)`, 'success');

    } catch (error) {
        console.error('刷新学生数据失败:', error);
        showMessage('刷新数据失败，请重试', 'error');
    }
}

// 静默刷新学生数据（不显示加载提示和成功消息）
async function refreshStudentDataSilently() {
    try {
        console.log('静默刷新学生数据...');

        // 清除缓存时间戳
        window.lastStudentDataLoadTime = 0;

        // 使用强化的API加载函数
        const students = await loadTeacherStudentsFromAPI(true);

        // 更新全局数据
        teacherStudents = students;

        // 记录刷新时间
        window.lastStudentDataLoadTime = Date.now();

        // 重新渲染表格
        const tbody = document.querySelector('#student-table-body');
        if (tbody) {
            tbody.innerHTML = renderStudentTableRows();
        }

        // 更新统计信息
        updateFilterStats(students.length, students.length);

        // 更新学生数量显示
        const countElement = document.querySelector('h5');
        if (countElement && countElement.textContent.includes('学生列表')) {
            countElement.innerHTML = `<i class="fas fa-users"></i> 学生列表 (${students.length}人)`;
        }

        // 清除选择状态
        clearSelection();

        // 初始化批量操作按钮状态
        updateBatchButtons();

        console.log(`静默刷新完成，当前显示 ${students.length} 个学生`);
        return students;

    } catch (error) {
        console.error('静默刷新学生数据失败:', error);
        return null;
    }
}

// 内联Excel文件上传处理（完整实现）
function setupInlineExcelFileUpload() {
    const fileInput = document.getElementById('excelFileInput');
    const uploadArea = fileInput?.parentElement;
    const confirmBtn = document.getElementById('confirmImportBtn');

    if (!fileInput || !confirmBtn) return;

    // 文件选择事件
    fileInput.addEventListener('change', handleInlineExcelFile);

    // 拖拽支持
    if (uploadArea) {
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('border-success');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('border-success');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('border-success');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleInlineExcelFile({ target: { files } });
            }
        });
    }

    // 确认导入事件
    confirmBtn.onclick = confirmInlineExcelImport;
}

// 处理Excel文件选择
async function handleInlineExcelFile(event) {
    const file = event.target.files[0];
    if (!file) return;

    const preview = document.getElementById('importPreview');
    const content = document.getElementById('previewContent');
    const confirmBtn = document.getElementById('confirmImportBtn');

    // 文件格式检查
    if (!file.name.match(/\.(xlsx|xls|csv)$/i)) {
        content.innerHTML = '<div class="alert alert-danger">请选择Excel文件（.xlsx、.xls）或CSV文件</div>';
        preview.style.display = 'block';
        confirmBtn.disabled = true;
        return;
    }

    // 文件大小检查
    if (file.size > 5 * 1024 * 1024) {
        content.innerHTML = '<div class="alert alert-danger">文件过大，请选择小于5MB的文件</div>';
        preview.style.display = 'block';
        confirmBtn.disabled = true;
        return;
    }

    // 显示解析中
    content.innerHTML = `
        <div class="d-flex align-items-center p-3">
            <div class="spinner-border text-primary me-3" role="status"></div>
            <div>
                <h6 class="mb-1">正在解析文件...</h6>
                <small class="text-muted">${file.name} (${(file.size / 1024).toFixed(2)} KB)</small>
            </div>
        </div>
    `;
    preview.style.display = 'block';
    confirmBtn.disabled = true;

    try {
        // 解析Excel文件
        const parsedData = await parseInlineExcelFile(file);
        if (parsedData && parsedData.students && parsedData.students.length > 0) {
            displayInlineImportPreview(parsedData, file.name);
            confirmBtn.disabled = false;
            window.inlineParsedData = parsedData; // 存储解析结果
        } else {
            content.innerHTML = `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> 未找到有效数据</h6>
                    <p class="mb-2">请检查：</p>
                    <ul class="mb-0">
                        <li>文件是否包含表头：学校名称、年级、班级、学生姓名</li>
                        <li>数据行是否填写完整</li>
                        <li>是否选择了正确的工作表</li>
                    </ul>
                </div>
            `;
            confirmBtn.disabled = true;
        }
    } catch (error) {
        console.error('文件解析失败:', error);
        content.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-times-circle"></i> 文件解析失败</h6>
                <p class="mb-2">错误信息：${error.message}</p>
                <small>请检查文件格式是否正确，或尝试重新保存为标准Excel格式</small>
            </div>
        `;
        confirmBtn.disabled = true;
    }
}

// 解析Excel文件（内联实现）
async function parseInlineExcelFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                let workbook;

                // 检查XLSX库
                if (typeof XLSX === 'undefined') {
                    // 尝试CSV解析作为兜底
                    if (file.name.toLowerCase().endsWith('.csv')) {
                        const csvText = e.target.result;
                        const result = parseCSVContent(csvText);
                        resolve(result);
                        return;
                    } else {
                        reject(new Error('Excel解析库未加载，请刷新页面重试'));
                        return;
                    }
                }

                // Excel解析
                const data = new Uint8Array(e.target.result);
                workbook = XLSX.read(data, { type: 'array' });

                if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                    reject(new Error('Excel文件中没有找到工作表'));
                    return;
                }

                // 读取第一个工作表
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                if (!jsonData || jsonData.length === 0) {
                    reject(new Error('工作表为空'));
                    return;
                }

                if (jsonData.length === 1) {
                    reject(new Error('只有表头，没有数据行'));
                    return;
                }

                // 解析数据
                const result = parseExcelJsonData(jsonData);
                resolve(result);

            } catch (error) {
                reject(new Error('Excel文件解析失败: ' + error.message));
            }
        };

        reader.onerror = () => reject(new Error('文件读取失败'));

        if (file.name.toLowerCase().endsWith('.csv')) {
            reader.readAsText(file, 'UTF-8');
        } else {
            reader.readAsArrayBuffer(file);
        }
    });
}

// 解析Excel JSON数据为学生记录
function parseExcelJsonData(jsonData) {
    const headers = jsonData[0];
    const students = [];
    const errors = [];

    // 查找列索引
    const colMap = {};
    headers.forEach((header, index) => {
        const h = String(header || '').trim();
        if (h.includes('学校') || h.includes('school')) colMap.school = index;
        if (h.includes('年级') || h.includes('grade')) colMap.grade = index;
        if (h.includes('班级') || h.includes('class')) colMap.class = index;
        if (h.includes('姓名') || h.includes('name')) colMap.name = index;
        if (h.includes('学号') || h.includes('id')) colMap.studentId = index;
        if (h.includes('性别') || h.includes('gender')) colMap.gender = index;
        if (h.includes('备注') || h.includes('remark')) colMap.remarks = index;
    });

    // 检查必需列
    if (colMap.school === undefined || colMap.grade === undefined ||
        colMap.class === undefined || colMap.name === undefined) {
        throw new Error('缺少必需的列：学校名称、年级、班级、学生姓名');
    }

    // 解析数据行
    for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (!row || row.length === 0) continue;

        try {
            const student = {
                schoolName: String(row[colMap.school] || '').trim(),
                grade: parseInt(row[colMap.grade]) || 0,
                className: parseInt(row[colMap.class]) || 0,
                studentName: String(row[colMap.name] || '').trim(),
                studentId: colMap.studentId !== undefined ? String(row[colMap.studentId] || '').trim() : '',
                gender: colMap.gender !== undefined ? String(row[colMap.gender] || '').trim() : '',
                remarks: colMap.remarks !== undefined ? String(row[colMap.remarks] || '').trim() : ''
            };

            // 基本验证
            if (!student.schoolName || !student.grade || !student.className || !student.studentName) {
                errors.push({ row: i + 1, error: '缺少必需信息（学校、年级、班级、姓名）', data: student });
                continue;
            }

            if (student.grade < 1 || student.grade > 12) {
                errors.push({ row: i + 1, error: '年级必须在1-12之间', data: student });
                continue;
            }

            if (student.className < 1 || student.className > 99) {
                errors.push({ row: i + 1, error: '班级必须在1-99之间', data: student });
                continue;
            }

            students.push(student);

        } catch (error) {
            errors.push({ row: i + 1, error: error.message, data: row });
        }
    }

    return {
        total: students.length,
        students: students,
        errors: errors,
        summary: {
            schools: [...new Set(students.map(s => s.schoolName))],
            grades: [...new Set(students.map(s => s.grade))].sort((a,b) => a-b),
            totalStudents: students.length,
            errorCount: errors.length
        }
    };
}

// 解析CSV内容（兜底方案）
function parseCSVContent(csvText) {
    const lines = csvText.split('\n').map(line => line.trim()).filter(line => line);
    if (lines.length === 0) throw new Error('CSV文件为空');
    if (lines.length === 1) throw new Error('CSV文件只有表头，没有数据行');

    const headers = lines[0].split(',').map(h => h.trim().replace(/['"]/g, ''));
    const jsonData = [headers];

    for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim().replace(/['"]/g, ''));
        jsonData.push(values);
    }

    return parseExcelJsonData(jsonData);
}
// 显示导入预览
function displayInlineImportPreview(parsedData, fileName) {
    const content = document.getElementById('previewContent');
    const { students, errors, summary } = parsedData;

    let html = `
        <div class="row mb-3">
            <div class="col-md-8">
                <h6><i class="fas fa-file-excel text-success"></i> ${fileName}</h6>
                <div class="row text-center">
                    <div class="col-3">
                        <div class="border rounded p-2">
                            <div class="h5 text-primary mb-0">${summary.totalStudents}</div>
                            <small class="text-muted">学生总数</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border rounded p-2">
                            <div class="h5 text-info mb-0">${summary.schools.length}</div>
                            <small class="text-muted">学校数</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border rounded p-2">
                            <div class="h5 text-warning mb-0">${summary.grades.length}</div>
                            <small class="text-muted">年级数</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border rounded p-2">
                            <div class="h5 ${errors.length ? 'text-danger' : 'text-success'} mb-0">${errors.length}</div>
                            <small class="text-muted">错误行数</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-info mb-0 p-2">
                    <small><strong>自动创建策略：</strong></small>
                    <ul class="mb-0 mt-1" style="font-size: 12px;">
                        <li>不存在的学校将自动创建</li>
                        <li>年级班级数将自动扩容</li>
                        <li>重复学生将更新信息</li>
                    </ul>
                </div>
            </div>
        </div>
    `;

    // 显示学校和年级摘要
    if (summary.schools.length > 0) {
        html += `
            <div class="mb-3">
                <h6>涉及学校和年级：</h6>
                <div class="d-flex flex-wrap gap-1">
                    ${summary.schools.map(school =>
                        `<span class="badge bg-success">${school}</span>`
                    ).join('')}
                </div>
                <div class="mt-1">
                    <small class="text-muted">年级：${summary.grades.join('、')}年级</small>
                </div>
            </div>
        `;
    }

    // 显示错误（如果有）
    if (errors.length > 0) {
        html += `
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> 发现 ${errors.length} 个错误行</h6>
                <div class="table-responsive" style="max-height: 200px;">
                    <table class="table table-sm">
                        <thead>
                            <tr><th>行号</th><th>错误信息</th><th>数据</th></tr>
                        </thead>
                        <tbody>
                            ${errors.slice(0, 10).map(err => `
                                <tr>
                                    <td>${err.row}</td>
                                    <td><small class="text-danger">${err.error}</small></td>
                                    <td><small>${JSON.stringify(err.data).slice(0, 50)}...</small></td>
                                </tr>
                            `).join('')}
                            ${errors.length > 10 ? `<tr><td colspan="3"><small class="text-muted">...还有 ${errors.length - 10} 个错误</small></td></tr>` : ''}
                        </tbody>
                    </table>
                </div>
                <small class="text-muted">错误行将被跳过，不会导入</small>
            </div>
        `;
    }

    // 显示前几行预览
    if (students.length > 0) {
        html += `
            <div class="mb-3">
                <h6>数据预览（前5行）：</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>学校</th><th>年级</th><th>班级</th><th>姓名</th><th>学号</th><th>性别</th><th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${students.slice(0, 5).map(s => `
                                <tr>
                                    <td>${s.schoolName}</td>
                                    <td>${s.grade}</td>
                                    <td>${s.className}</td>
                                    <td>${s.studentName}</td>
                                    <td>${s.studentId || '-'}</td>
                                    <td>${s.gender || '-'}</td>
                                    <td>${s.remarks || '-'}</td>
                                </tr>
                            `).join('')}
                            ${students.length > 5 ? `<tr><td colspan="7"><small class="text-muted text-center">...还有 ${students.length - 5} 行数据</small></td></tr>` : ''}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    content.innerHTML = html;
}

// 确认导入
async function confirmInlineExcelImport() {
    const parsedData = window.inlineParsedData;
    if (!parsedData || !parsedData.students || parsedData.students.length === 0) {
        alert('没有可导入的数据');
        return;
    }

    const confirmBtn = document.getElementById('confirmImportBtn');
    const content = document.getElementById('previewContent');

    // 确认对话框
    if (!confirm(`确定要导入 ${parsedData.students.length} 个学生吗？\n\n这将会：\n• 自动创建不存在的学校\n• 自动扩容年级班级数\n• 更新已存在的学生信息`)) {
        return;
    }

    // 显示导入进度
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导入中...';

    content.innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary mb-3" role="status"></div>
            <h6>正在导入学生数据...</h6>
            <div class="progress mt-3" style="height: 20px;">
                <div class="progress-bar" role="progressbar" style="width: 0%">0%</div>
            </div>
            <div id="importStatus" class="mt-2">
                <small class="text-muted">准备导入...</small>
            </div>
        </div>
    `;

    try {
        const results = await batchImportStudents(parsedData.students);

        // 显示导入结果
        content.innerHTML = `
            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle"></i> 导入完成！</h6>
                <div class="row text-center mt-3">
                    <div class="col-3">
                        <div class="h5 text-success mb-0">${results.success}</div>
                        <small>成功导入</small>
                    </div>
                    <div class="col-3">
                        <div class="h5 text-danger mb-0">${results.failed}</div>
                        <small>跳过/失败</small>
                    </div>
                    <div class="col-3">
                        <div class="h5 text-info mb-0">${results.schoolsCreated}</div>
                        <small>创建学校</small>
                    </div>
                    <div class="col-3">
                        <div class="h5 text-warning mb-0">${results.gradesExpanded}</div>
                        <small>扩容年级</small>
                    </div>
                </div>
                ${results.errors.length > 0 ? `
                    <div class="mt-3">
                        <h6>跳过/错误详情：</h6>
                        <div style="max-height: 150px; overflow-y: auto;">
                            ${results.errors.map(err => {
                                const isDuplicate = err.includes('已存在');
                                const iconClass = isDuplicate ? 'text-warning' : 'text-danger';
                                const icon = isDuplicate ? '⚠️' : '❌';
                                return `<div class="${iconClass} small">${icon} ${err}</div>`;
                            }).join('')}
                        </div>
                        <small class="text-muted mt-2 d-block">
                            ⚠️ 表示重复学生已跳过，❌ 表示导入失败
                        </small>
                    </div>
                ` : ''}
            </div>
        `;

        confirmBtn.innerHTML = '<i class="fas fa-check"></i> 导入完成';

        // 刷新学生列表和界面
        setTimeout(async () => {
            try {
                console.log('开始刷新数据...');

                const oldStudentCount = teacherStudents?.length || 0;
                console.log('导入前学生数量:', oldStudentCount);

                // 使用修复后的加载函数（已包含防缓存）
                teacherStudents = await loadTeacherStudentsFromAPI(true);
                teacherSchools = await loadTeacherSchoolsFromAPI();
                teacherClasses = await loadTeacherClassesFromAPI();

                const newStudentCount = teacherStudents?.length || 0;
                console.log('数据刷新完成，新学生数量:', newStudentCount, '增加了:', newStudentCount - oldStudentCount);

                if (newStudentCount === oldStudentCount) {
                    console.warn('警告：学生数量没有增加，可能存在以下问题：');
                    console.warn('1. 后端创建学生失败但返回了成功状态');
                    console.warn('2. 权限问题：创建的学生不在当前教师的可见范围内');
                    console.warn('3. 数据库事务问题：创建后立即查询可能看不到新数据');
                    console.warn('4. 缓存问题：后端或数据库层面的缓存延迟');
                }

                // 强制刷新学生管理界面
                if (currentSection === 'student-management') {
                    console.log('刷新学生管理界面');
                    showStudentManagement();
                } else {
                    console.log('当前不在学生管理页面，切换到学生管理');
                    showTeacherSection('student-management');
                }

                // 关闭导入模态
                const modal = document.getElementById('excelImportModal');
                if (modal) {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) bsModal.hide();
                }

            } catch (e) {
                console.error('刷新数据失败', e);
                alert('导入成功，但刷新界面失败，请手动刷新页面查看结果');
            }
        }, 1500);

    } catch (error) {
        console.error('批量导入失败:', error);
        content.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-times-circle"></i> 导入失败</h6>
                <p>错误信息：${error.message}</p>
                <small>请检查网络连接或联系管理员</small>
            </div>
        `;
        confirmBtn.innerHTML = '<i class="fas fa-upload"></i> 重试导入';
        confirmBtn.disabled = false;
    }
}

// 批量导入学生（使用已有的自动创建逻辑）
async function batchImportStudents(students) {
    const results = {
        success: 0,
        failed: 0,
        schoolsCreated: 0,
        gradesExpanded: 0,
        errors: []
    };

    const progressBar = document.querySelector('.progress-bar');
    const statusDiv = document.getElementById('importStatus');
    const createdSchools = new Set();
    const expandedGrades = new Set();

    for (let i = 0; i < students.length; i++) {
        const student = students[i];
        const progress = Math.round(((i + 1) / students.length) * 100);

        // 更新进度
        if (progressBar) {
            progressBar.style.width = progress + '%';
            progressBar.textContent = progress + '%';
        }
        if (statusDiv) {
            statusDiv.innerHTML = `<small class="text-muted">正在导入 ${student.studentName} (${i + 1}/${students.length})</small>`;
        }

        try {
            // 直接实现完整导入逻辑（避免函数顺序问题）

            // 1. 确保学校存在（内联实现）
            let schoolInfo = null;
            let schoolId = null;

            // 先从本地缓存查找学校
            const existingSchool = (Array.isArray(teacherSchools) ? teacherSchools : [])
                .find(s => s && s.name === student.schoolName);

            if (existingSchool && existingSchool.id) {
                schoolInfo = existingSchool;
                schoolId = existingSchool.id;
            } else {
                // 尝试创建学校
                try {
                    const resp = await authFetch(`/api/teacher/schools?ts=${Date.now()}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ name: student.schoolName })
                    });
                    let json = {};
                    try { json = await resp.json(); } catch (_) {}
                    if (resp.ok && json && (json.success !== false)) {
                        schoolInfo = json.data || json;
                        schoolId = schoolInfo.id;
                        createdSchools.add(student.schoolName);
                        // 刷新本地缓存
                        try { teacherSchools = await loadTeacherSchoolsFromAPI(); } catch (e) { console.warn('刷新学校缓存失败', e); }
                    }
                } catch (e) {
                    console.warn('创建学校失败', e);
                }
            }

            // 2. 确保年级配置满足班级数要求（内联实现）
            if (schoolId) {
                try {
                    const needClassNo = parseInt(student.className) || 1;
                    const ts = Date.now();

                    // 获取当前年级配置
                    let list = [];
                    try {
                        const resp = await authFetch(`/api/teacher/schools/${schoolId}/grades?ts=${ts}`, {});
                        let resj = {};
                        try { resj = await resp.json(); } catch (_) {}
                        list = Array.isArray(resj) ? resj : (Array.isArray(resj.data) ? resj.data : []);
                    } catch (e) { list = []; }

                    const cfg = list.find(c => String(c.grade) === String(student.grade));
                    const current = cfg ? (parseInt(cfg.class_count) || 0) : 0;

                    if (!cfg || current < needClassNo) {
                        const post = await authFetch(`/api/teacher/schools/${schoolId}/grades/${student.grade}`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ class_count: needClassNo })
                        });
                        if (post.ok) {
                            expandedGrades.add(`${student.schoolName}-${student.grade}`);
                        }
                    }
                } catch (e) {
                    console.warn('年级配置更新失败', e);
                }
            }

            // 3. 检查学生是否已存在（仅当所有信息完全一致时才跳过）
            const isDuplicate = await checkStudentFullDuplicate(student);
            if (isDuplicate) {
                console.warn(`学生信息完全重复，跳过: ${student.studentName} (${student.schoolName} ${student.grade}年级${student.className}班)`);
                results.failed++;
                results.errors.push(`${student.studentName}: 该学生的所有信息都与现有记录完全一致，已跳过重复导入`);

                // 跳过这个学生，继续处理下一个
                if ((i + 1) % 10 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                continue;
            }

            // 4. 提交学生数据（移除 group_number 字段，因为数据库表中不存在该列）
            let requestBody;
            if (schoolId) {
                requestBody = {
                    name: student.studentName,
                    student_id: student.studentId || null,
                    school_id: parseInt(schoolId),
                    grade: parseInt(student.grade),
                    class: parseInt(student.className),
                    gender: student.gender || null,
                    remarks: student.remarks || null
                };
            } else {
                requestBody = {
                    name: student.studentName,
                    student_id: student.studentId || null,
                    school_name: student.schoolName,
                    grade: parseInt(student.grade),
                    class: parseInt(student.className),
                    gender: student.gender || null,
                    remarks: student.remarks || null
                };
            }

            const response = await authFetch('/api/teacher/students', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('学生创建失败详情:', {
                    status: response.status,
                    statusText: response.statusText,
                    requestBody: requestBody,
                    errorData: errorData
                });
                throw new Error(errorData.message || errorData.error || `添加学生失败: HTTP ${response.status} - ${response.statusText}`);
            }

            const result = await response.json().catch(() => ({}));
            console.log('学生创建成功:', student.studentName, result);

            results.success++;

            // 记录创建的学校和扩容的年级（简化统计）
            if (!teacherSchools.find(s => s.name === student.schoolName)) {
                createdSchools.add(student.schoolName);
            }
            expandedGrades.add(`${student.schoolName}-${student.grade}`);

            // 自动为教师添加对新创建班级的任教权限
            if (schoolId) {
                try {
                    await addTeacherClassPermission(parseInt(schoolId), parseInt(student.grade), parseInt(student.className));
                } catch (e) {
                    console.warn('添加任教权限失败:', e);
                }
            }

        } catch (error) {
            console.error(`导入学生 ${student.studentName} 失败:`, error);
            results.failed++;
            results.errors.push(`${student.studentName}: ${error.message}`);
        }

        // 避免请求过快，每10个学生暂停一下
        if ((i + 1) % 10 === 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    results.schoolsCreated = createdSchools.size;
    results.gradesExpanded = expandedGrades.size;

    return results;
}

// 为教师添加班级任教权限（避免重复添加）
async function addTeacherClassPermission(schoolId, grade, classNo) {
    try {
        // 先检查是否已有该权限
        const checkResponse = await authFetch(`/api/teacher/class-permissions?school_id=${schoolId}&grade=${grade}&class=${classNo}`, {});
        if (checkResponse.ok) {
            const existing = await checkResponse.json();
            if (existing && (existing.length > 0 || existing.data?.length > 0)) {
                console.log(`任教权限已存在: 学校${schoolId} ${grade}年级${classNo}班`);
                return true; // 已存在，视为成功
            }
        }

        // 不存在则添加
        const response = await authFetch('/api/teacher/class-permissions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                school_id: schoolId,
                grade: grade,
                class: classNo
            })
        });

        if (response.ok) {
            console.log(`已添加任教权限: 学校${schoolId} ${grade}年级${classNo}班`);
            return true;
        } else {
            const errorData = await response.json().catch(() => ({}));
            // 如果是重复键错误，也视为成功
            if (errorData.error && errorData.error.includes('duplicate key')) {
                console.log(`任教权限已存在（重复键）: 学校${schoolId} ${grade}年级${classNo}班`);
                return true;
            }
            console.warn('添加任教权限失败:', errorData);
            return false;
        }
    } catch (error) {
        console.error('添加任教权限异常:', error);
        return false;
    }
}
// 检查学生是否完全重复（所有字段都一致才算重复）
async function checkStudentFullDuplicate(student) {
    try {
        // 从本地缓存检查（快速）
        if (teacherStudents && Array.isArray(teacherStudents)) {
            const existing = teacherStudents.find(s => {
                // 比较所有关键字段
                const schoolMatch = s.school_name === student.schoolName;
                const gradeMatch = parseInt(s.grade) === parseInt(student.grade);
                const classMatch = parseInt(s.class) === parseInt(student.className);
                const nameMatch = s.name === student.studentName;

                // 比较可选字段（空值和undefined视为相等）
                const studentIdMatch = normalizeValue(s.student_id) === normalizeValue(student.studentId);
                const genderMatch = normalizeValue(s.gender) === normalizeValue(student.gender);
                const remarksMatch = normalizeValue(s.remarks) === normalizeValue(student.remarks);

                return schoolMatch && gradeMatch && classMatch && nameMatch &&
                       studentIdMatch && genderMatch && remarksMatch;
            });

            if (existing) {
                console.log(`本地缓存发现完全重复学生: ${student.studentName}`, {
                    existing: {
                        school: existing.school_name,
                        grade: existing.grade,
                        class: existing.class,
                        name: existing.name,
                        student_id: existing.student_id,
                        gender: existing.gender,
                        remarks: existing.remarks
                    },
                    importing: {
                        school: student.schoolName,
                        grade: student.grade,
                        class: student.className,
                        name: student.studentName,
                        student_id: student.studentId,
                        gender: student.gender,
                        remarks: student.remarks
                    }
                });
                return true;
            }
        }

        return false; // 没有找到完全重复的记录
    } catch (error) {
        console.warn('检查学生完全重复性异常:', error);
        return false; // 异常时允许添加，避免阻塞
    }
}

// 标准化值，将空字符串、null、undefined都视为相等
function normalizeValue(value) {
    if (value === null || value === undefined || value === '') {
        return null;
    }
    return String(value).trim();
}

// =============== 批量操作功能 ===============







// 获取选中的学生ID列表
function getSelectedStudentIds() {
    const checkboxes = document.querySelectorAll('.student-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 批量转班
function showBatchTransferModal() {
    const selectedIds = getSelectedStudentIds();
    if (selectedIds.length === 0) {
        alert('请先选择要转班的学生');
        return;
    }

    const modalHtml = `
        <div class="modal fade" id="batchTransferModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-exchange-alt"></i> 批量转班
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            即将转移 <strong>${selectedIds.length}</strong> 个学生到新的班级
                        </div>

                        <form id="batchTransferForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="transferSchool" class="form-label">目标学校 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="transferSchool" required onchange="loadTransferGrades()">
                                            <option value="">请选择学校</option>
                                            ${(teacherSchools || []).map(school =>
                                                `<option value="${school.id}">${school.name}</option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="transferGrade" class="form-label">目标年级 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="transferGrade" required onchange="loadTransferClasses()" disabled>
                                            <option value="">请先选择学校</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="transferClass" class="form-label">目标班级 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="transferClass" required disabled>
                                            <option value="">请先选择年级</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="transferReason" class="form-label">转班原因</label>
                                <textarea class="form-control" id="transferReason" rows="3"
                                          placeholder="请输入转班原因（可选）"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-info" onclick="executeBatchTransfer()">
                            <i class="fas fa-exchange-alt"></i> 确认转班
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('batchTransferModal'));
    modal.show();

    // 模态框关闭时清理DOM
    document.getElementById('batchTransferModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 加载转班目标年级
async function loadTransferGrades() {
    const schoolId = document.getElementById('transferSchool').value;
    const gradeSelect = document.getElementById('transferGrade');
    const classSelect = document.getElementById('transferClass');

    // 重置下级选择
    gradeSelect.innerHTML = '<option value="">加载中...</option>';
    gradeSelect.disabled = true;
    classSelect.innerHTML = '<option value="">请先选择年级</option>';
    classSelect.disabled = true;

    if (!schoolId) {
        gradeSelect.innerHTML = '<option value="">请先选择学校</option>';
        return;
    }

    try {
        const response = await authFetch(`/api/teacher/schools/${schoolId}/grades`);
        if (response.ok) {
            const grades = await response.json();
            gradeSelect.innerHTML = '<option value="">请选择年级</option>' +
                (Array.isArray(grades) ? grades : []).map(grade =>
                    `<option value="${grade.grade}">${grade.grade}年级</option>`
                ).join('');
            gradeSelect.disabled = false;
        } else {
            gradeSelect.innerHTML = '<option value="">加载失败</option>';
        }
    } catch (error) {
        console.error('加载年级失败:', error);
        gradeSelect.innerHTML = '<option value="">加载失败</option>';
    }
}

// 加载转班目标班级
function loadTransferClasses() {
    const grade = document.getElementById('transferGrade').value;
    const classSelect = document.getElementById('transferClass');

    if (!grade) {
        classSelect.innerHTML = '<option value="">请先选择年级</option>';
        classSelect.disabled = true;
        return;
    }

    // 根据年级生成班级选项（1-20班）
    const classOptions = [];
    for (let i = 1; i <= 20; i++) {
        classOptions.push(`<option value="${i}">${i}班</option>`);
    }

    classSelect.innerHTML = '<option value="">请选择班级</option>' + classOptions.join('');
    classSelect.disabled = false;
}

// 执行批量转班
async function executeBatchTransfer() {
    const selectedIds = getSelectedStudentIds();
    const schoolId = document.getElementById('transferSchool').value;
    const grade = document.getElementById('transferGrade').value;
    const classNo = document.getElementById('transferClass').value;
    const reason = document.getElementById('transferReason').value;

    if (!schoolId || !grade || !classNo) {
        alert('请完整填写目标学校、年级和班级');
        return;
    }

    if (!confirm(`确定要将 ${selectedIds.length} 个学生转到新班级吗？\n\n目标：${document.getElementById('transferSchool').selectedOptions[0].text} ${grade}年级${classNo}班`)) {
        return;
    }

    try {
        const response = await authFetch('/api/teacher/students/batch-transfer', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                student_ids: selectedIds,
                target_school_id: parseInt(schoolId),
                target_grade: parseInt(grade),
                target_class: parseInt(classNo),
                reason: reason
            })
        });

        if (response.ok) {
            const result = await response.json();
            alert(`批量转班成功！转移了 ${result.transferred_count || selectedIds.length} 个学生`);

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchTransferModal'));
            modal.hide();

            // 刷新学生列表
            await refreshStudentData();
        } else {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || '批量转班失败');
        }
    } catch (error) {
        console.error('批量转班失败:', error);
        alert('批量转班失败: ' + error.message);
    }
}


// 查看学生详情
function viewStudent(studentId) {
    const student = teacherStudents.find(s => String(s.id) === String(studentId));
    if (!student) {
        alert('学生信息不存在');
        return;
    }

    const school = teacherSchools.find(s => s.id == student.school_id);
    const schoolName = school ? school.name : '未知学校';

    const modalHtml = `
        <div class="modal fade" id="viewStudentModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-eye"></i> 学生详情
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <h5>${student.name || student.student_name || '未知'}</h5>
                                <p class="text-muted">${student.student_id || student.student_identifier || '无学号'}</p>
                            </div>

                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">学校</label>
                                            <div class="info-value">${schoolName}</div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">年级</label>
                                            <div class="info-value">${student.grade || student.student_grade || '-'}年级</div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">班级</label>
                                            <div class="info-value">${student.class || student.student_class || '-'}班</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">性别</label>
                                            <div class="info-value">${student.gender || '-'}</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">座位号</label>
                                            <div class="info-value">${student.seat_number || '-'}</div>
                                        </div>
                                    </div>

                                    <div class="col-md-12">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">备注</label>
                                            <div class="info-value">${student.remarks || '-'}</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">创建时间</label>
                                            <div class="info-value">${formatDateTime(student.created_at)}</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="info-item mb-3">
                                            <label class="form-label text-muted">更新时间</label>
                                            <div class="info-value">${formatDateTime(student.updated_at)}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="editStudent(${studentId})" data-bs-dismiss="modal">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('viewStudentModal'));
    modal.show();

    // 模态框关闭时清理DOM
    document.getElementById('viewStudentModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}
/**
 * 渲染学生表格行
// 编辑学生信息
function editStudent(studentId) {
    const student = teacherStudents.find(s => String(s.id) === String(studentId));
    if (!student) {
        alert('学生信息不存在');
        return;
    }

    const school = teacherSchools.find(s => s.id == student.school_id);
    const schoolName = school ? school.name : '未知学校';

    const modalHtml = `
        <div class="modal fade" id="editStudentModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-edit"></i> 编辑学生信息
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editStudentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editStudentName" class="form-label">学生姓名 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="editStudentName"
                                               value="${student.name || student.student_name || ''}" required>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editStudentId" class="form-label">学号</label>
                                        <input type="text" class="form-control" id="editStudentId"
                                               value="${student.student_id || student.student_identifier || ''}">
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="editSchool" class="form-label">学校 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="editSchool" required onchange="loadEditGrades()">
                                            <option value="">请选择学校</option>
                                            ${(teacherSchools || []).map(s =>
                                                `<option value="${s.id}" ${s.id == student.school_id ? 'selected' : ''}>${s.name}</option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="editGrade" class="form-label">年级 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="editGrade" required onchange="loadEditClasses()">
                                            <option value="${student.grade || student.student_grade || ''}" selected>
                                                ${student.grade || student.student_grade || ''}年级
                                            </option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="editClass" class="form-label">班级 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="editClass" required>
                                            <option value="${student.class || student.student_class || ''}" selected>
                                                ${student.class || student.student_class || ''}班
                                            </option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editGender" class="form-label">性别</label>
                                        <select class="form-select" id="editGender">
                                            <option value="">请选择</option>
                                            <option value="男" ${student.gender === '男' ? 'selected' : ''}>男</option>
                                            <option value="女" ${student.gender === '女' ? 'selected' : ''}>女</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editSeatNumber" class="form-label">座位号</label>
                                        <input type="number" class="form-control" id="editSeatNumber"
                                               value="${student.seat_number || ''}" min="1" max="99">
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="editRemarks" class="form-label">备注</label>
                                        <textarea class="form-control" id="editRemarks" rows="3">${student.remarks || ''}</textarea>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveStudentEdit(${studentId})">
                            <i class="fas fa-save"></i> 保存修改
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('editStudentModal'));
    modal.show();

    // 初始化年级和班级选项
    if (student.school_id) {
        loadEditGrades();
    }

    // 模态框关闭时清理DOM
    document.getElementById('editStudentModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}
// 加载编辑模态框的年级选项
async function loadEditGrades() {
    const schoolId = document.getElementById('editSchool').value;
    const gradeSelect = document.getElementById('editGrade');
    const classSelect = document.getElementById('editClass');

    if (!schoolId) {
        gradeSelect.innerHTML = '<option value="">请先选择学校</option>';
        classSelect.innerHTML = '<option value="">请先选择年级</option>';
        return;
    }

    try {
        const response = await authFetch(`/api/teacher/schools/${schoolId}/grades`);
        if (response.ok) {
            const grades = await response.json();
            gradeSelect.innerHTML = '<option value="">请选择年级</option>' +
                (Array.isArray(grades) ? grades : []).map(grade =>
                    `<option value="${grade.grade}">${grade.grade}年级</option>`
                ).join('');
        }
    } catch (error) {
        console.error('加载年级失败:', error);
        gradeSelect.innerHTML = '<option value="">加载失败</option>';
    }
}

// 加载编辑模态框的班级选项
function loadEditClasses() {
    const grade = document.getElementById('editGrade').value;
    const classSelect = document.getElementById('editClass');

    if (!grade) {
        classSelect.innerHTML = '<option value="">请先选择年级</option>';
        return;
    }

    // 生成1-20班的选项
    const classOptions = [];
    for (let i = 1; i <= 20; i++) {
        classOptions.push(`<option value="${i}">${i}班</option>`);
    }

    classSelect.innerHTML = '<option value="">请选择班级</option>' + classOptions.join('');
}

// 保存学生编辑
async function saveStudentEdit(studentId) {
    const name = document.getElementById('editStudentName').value.trim();
    const studentIdValue = document.getElementById('editStudentId').value.trim();
    const schoolId = document.getElementById('editSchool').value;
    const grade = document.getElementById('editGrade').value;
    const classNo = document.getElementById('editClass').value;
    const gender = document.getElementById('editGender').value;
    const seatNumber = document.getElementById('editSeatNumber').value;
    const remarks = document.getElementById('editRemarks').value.trim();

    if (!name || !schoolId || !grade || !classNo) {
        alert('请填写必填字段：姓名、学校、年级、班级');
        return;
    }

    try {
        const response = await authFetch(`/api/teacher/students/${studentId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                name: name,
                student_id: studentIdValue || null,
                school_id: parseInt(schoolId),
                grade: parseInt(grade),
                class: parseInt(classNo),
                gender: gender || null,
                seat_number: seatNumber ? parseInt(seatNumber) : null,
                remarks: remarks || null
            })
        });

        if (response.ok) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editStudentModal'));
            modal.hide();

            // 静默刷新学生列表
            const updatedStudents = await refreshStudentDataSilently();

            if (updatedStudents) {
                showMessage('学生信息更新成功', 'success');
            } else {
                showMessage('学生信息更新成功，但列表刷新失败，请手动刷新', 'warning');
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || '更新失败');
        }
    } catch (error) {
        console.error('更新学生信息失败:', error);
        alert('更新失败: ' + error.message);
    }
}

// 删除学生
async function deleteStudent(studentId, studentName) {
    if (!confirm(`确定要删除学生 "${studentName}" 吗？\n\n此操作不可撤销！`)) {
        return;
    }

    try {
        const response = await authFetch(`/api/teacher/students/${studentId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            // 静默刷新学生列表
            const updatedStudents = await refreshStudentDataSilently();

            if (updatedStudents) {
                showMessage(`学生"${studentName}"删除成功`, 'success');
            } else {
                showMessage('学生删除成功，但列表刷新失败，请手动刷新', 'warning');
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || '删除失败');
        }
    } catch (error) {
        console.error('删除学生失败:', error);
        alert('删除失败: ' + error.message);
    }
}
 */
// =============== 批量操作功能 ===============

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');

    studentCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBatchButtons();
}

// 更新批量操作按钮状态
function updateBatchButtons() {
    const selectedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
    const batchButtonsInline = document.getElementById('batchOperationButtonsInline');
    const batchStatusBadge = document.getElementById('batchStatusBadge');
    const selectedCountInline = document.getElementById('selectedCountInline');
    const batchInfo = document.getElementById('batchSelectionInfo');
    const selectedCount = document.getElementById('selectedCount');

    // 只有选择学生时才显示批量操作（允许单个学生操作）
    if (selectedCheckboxes.length > 0) {
        // 显示内联批量操作按钮
        if (batchButtonsInline) {
            batchButtonsInline.style.display = 'block';
        }

        // 显示状态徽章
        if (batchStatusBadge) {
            batchStatusBadge.style.display = 'inline-block';
        }

        // 显示批量选择信息（如果存在）
        if (batchInfo) {
            batchInfo.style.display = 'block';
        }

        // 更新选择数量显示
        if (selectedCountInline) {
            selectedCountInline.textContent = selectedCheckboxes.length;
        }
        if (selectedCount) {
            selectedCount.textContent = selectedCheckboxes.length;
        }
    } else {
        // 隐藏所有批量操作相关元素
        if (batchButtonsInline) {
            batchButtonsInline.style.display = 'none';
        }
        if (batchStatusBadge) {
            batchStatusBadge.style.display = 'none';
        }
        if (batchInfo) {
            batchInfo.style.display = 'none';
        }
    }

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('selectAll');
    const allCheckboxes = document.querySelectorAll('.student-checkbox');
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        selectAllCheckbox.checked = selectedCheckboxes.length === allCheckboxes.length;
        selectAllCheckbox.indeterminate = selectedCheckboxes.length > 0 && selectedCheckboxes.length < allCheckboxes.length;
    }
}

// 清除选择
function clearSelection() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }

    updateBatchButtons();
}

// 获取选中的学生ID
function getSelectedStudentIds() {
    const selectedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
    return Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
}

// 批量删除学生
async function batchDeleteStudents() {
    const selectedIds = getSelectedStudentIds();

    if (selectedIds.length === 0) {
        showMessage('请先选择要删除的学生', 'warning');
        return;
    }



    // 显示确认对话框
    const confirmModal = document.createElement('div');
    confirmModal.className = 'modal fade';
    confirmModal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle"></i> 确认批量删除
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-3">您确定要删除选中的 <strong>${selectedIds.length}</strong> 个学生吗？</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>警告：</strong>此操作不可撤销，删除后将无法恢复学生数据！
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmBatchDelete()">
                        <i class="fas fa-trash"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(confirmModal);
    const modal = new bootstrap.Modal(confirmModal);
    modal.show();

    // 模态框关闭时清理DOM
    confirmModal.addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 确认批量删除
async function confirmBatchDelete() {
    const selectedIds = getSelectedStudentIds();

    try {
        // 关闭确认对话框
        const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
        if (modal) modal.hide();

        // 显示进度提示
        showMessage('正在删除学生...', 'info');

        // 执行批量删除
        console.log('准备删除的学生ID:', selectedIds);
        const response = await authFetch('/api/teacher/students/batch-delete', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ studentIds: selectedIds })
        });

        console.log('删除API响应状态:', response.status);
        const responseData = await response.json();
        console.log('删除API响应数据:', responseData);

        if (response.ok) {
            console.log(`批量删除成功，删除了 ${responseData.deleted_count || selectedIds.length} 个学生`);

            // 静默刷新学生列表
            const updatedStudents = await refreshStudentDataSilently();

            // 清除选择状态
            clearSelection();

            if (updatedStudents) {
                showMessage(`成功删除 ${responseData.deleted_count || selectedIds.length} 个学生`, 'success');
            } else {
                showMessage(`删除成功，但列表刷新失败，请手动刷新`, 'warning');
            }
        } else {
            throw new Error(responseData.message || '批量删除失败');
        }
    } catch (error) {
        console.error('批量删除失败:', error);
        showMessage('批量删除失败: ' + error.message, 'error');
    }
}
// 批量编辑学生
function showBatchEditModal() {
    const selectedIds = getSelectedStudentIds();

    if (selectedIds.length === 0) {
        showMessage('请先选择要编辑的学生', 'warning');
        return;
    }



    const modalHtml = `
        <div class="modal fade" id="batchEditModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-edit"></i> 批量编辑学生 (${selectedIds.length}个)
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="batchEditForm">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                只有选中的字段会被更新，未选中的字段保持原值不变
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="updateSchool">
                                            <label class="form-check-label" for="updateSchool">
                                                更新学校
                                            </label>
                                        </div>
                                        <select class="form-select mt-2" id="batchSchool" disabled>
                                            <option value="">请选择学校</option>
                                            ${(teacherSchools || []).map(school =>
                                                `<option value="${school.id}">${school.name}</option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="updateGrade">
                                            <label class="form-check-label" for="updateGrade">
                                                更新年级
                                            </label>
                                        </div>
                                        <select class="form-select mt-2" id="batchGrade" disabled>
                                            <option value="">请选择年级</option>
                                            ${Array.from({length: 6}, (_, i) => i + 1).map(grade =>
                                                `<option value="${grade}">${grade}年级</option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="updateClass">
                                            <label class="form-check-label" for="updateClass">
                                                更新班级
                                            </label>
                                        </div>
                                        <select class="form-select mt-2" id="batchClass" disabled>
                                            <option value="">请选择班级</option>
                                            ${Array.from({length: 20}, (_, i) => i + 1).map(cls =>
                                                `<option value="${cls}">${cls}班</option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="updateGender">
                                            <label class="form-check-label" for="updateGender">
                                                更新性别
                                            </label>
                                        </div>
                                        <select class="form-select mt-2" id="batchGender" disabled>
                                            <option value="">请选择性别</option>
                                            <option value="男">男</option>
                                            <option value="女">女</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="updateRemarks">
                                            <label class="form-check-label" for="updateRemarks">
                                                更新备注
                                            </label>
                                        </div>
                                        <textarea class="form-control mt-2" id="batchRemarks" rows="3" disabled placeholder="输入备注信息"></textarea>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-warning" onclick="confirmBatchEdit()">
                            <i class="fas fa-save"></i> 保存修改
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('batchEditModal'));
    modal.show();

    // 绑定复选框事件
    ['updateSchool', 'updateGrade', 'updateClass', 'updateGender', 'updateRemarks'].forEach(checkboxId => {
        const checkbox = document.getElementById(checkboxId);
        const fieldId = checkboxId.replace('update', 'batch');
        const field = document.getElementById(fieldId);

        checkbox.addEventListener('change', function() {
            field.disabled = !this.checked;
            if (!this.checked) {
                field.value = '';
            }
        });
    });

    // 模态框关闭时清理DOM
    document.getElementById('batchEditModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 确认批量编辑
async function confirmBatchEdit() {
    const selectedIds = getSelectedStudentIds();
    const updates = {};

    // 收集要更新的字段
    if (document.getElementById('updateSchool').checked) {
        updates.school_id = document.getElementById('batchSchool').value;
        if (!updates.school_id) {
            showMessage('请选择学校', 'warning');
            return;
        }
    }

    if (document.getElementById('updateGrade').checked) {
        updates.grade = document.getElementById('batchGrade').value;
        if (!updates.grade) {
            showMessage('请选择年级', 'warning');
            return;
        }
    }

    if (document.getElementById('updateClass').checked) {
        updates.class = document.getElementById('batchClass').value;
        if (!updates.class) {
            showMessage('请选择班级', 'warning');
            return;
        }
    }

    if (document.getElementById('updateGender').checked) {
        updates.gender = document.getElementById('batchGender').value;
    }

    if (document.getElementById('updateRemarks').checked) {
        updates.remarks = document.getElementById('batchRemarks').value;
    }

    if (Object.keys(updates).length === 0) {
        showMessage('请至少选择一个要更新的字段', 'warning');
        return;
    }

    try {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('batchEditModal'));
        modal.hide();

        // 显示进度提示
        showMessage('正在批量更新学生信息...', 'info');

        // 执行批量更新
        const response = await authFetch('/api/teacher/students/batch-update', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                studentIds: selectedIds,
                updates: updates
            })
        });

        if (response.ok) {
            showMessage(`成功更新 ${selectedIds.length} 个学生信息`, 'success');

            // 刷新学生列表
            await refreshStudentData();

            // 清除选择状态
            clearSelection();
        } else {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || '批量更新失败');
        }
    } catch (error) {
        console.error('批量更新失败:', error);
        showMessage('批量更新失败: ' + error.message, 'error');
    }
}

function renderStudentTableRows() {
    if (!teacherStudents || teacherStudents.length === 0) {
        return `
            <tr>
                <td colspan="6" style="padding: 40px; text-align: center; color: #666;">
                    <div>
                        <i class="fas fa-users fa-3x mb-3" style="opacity: 0.3;"></i>
                        <h6>暂无学生数据</h6>
                        <p class="mb-3">您还没有添加任何学生信息</p>
                        <div class="btn-group">
                            <button onclick="showAddStudentModal()" class="btn btn-success btn-sm">
                                <i class="fas fa-user-plus"></i> 添加学生
                            </button>
                            <button onclick="showExcelImportModal()" class="btn btn-primary btn-sm">
                                <i class="fas fa-file-excel"></i> Excel导入
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    // 性能优化：预先构建学校映射，避免重复查找
    const schoolMap = new Map();
    if (teacherSchools) {
        teacherSchools.forEach(school => {
            schoolMap.set(school.id, school.name);
        });
    }

    return teacherStudents.map(student => {
        const studentName = student.name || student.student_name || '未知';
        const studentGrade = student.grade || student.student_grade || '未知';
        const studentClass = student.class || student.student_class || '未知';
        const schoolId = student.school_id || student.school || '';

        // 使用映射快速查找学校名称
        const schoolName = schoolMap.get(parseInt(schoolId)) || '未知学校';

        return `
            <tr>
                <td style="padding: 15px; border: none; vertical-align: middle;">
                    <input type="checkbox" class="student-checkbox" value="${student.id}" onchange="updateBatchButtons()">
                </td>
                <td style="padding: 15px; border: none; vertical-align: middle;">
                    ${schoolName}
                </td>
                <td style="padding: 15px; border: none; vertical-align: middle;">
                    ${studentGrade}年级${studentClass}班
                </td>
                <td style="padding: 15px; border: none; vertical-align: middle;">
                    <strong>${studentName}</strong>
                </td>
                <td style="padding: 15px; border: none; vertical-align: middle; text-align: center;">
                    <div class="btn-group">
                        <button onclick="viewStudent('${student.id}')" class="btn btn-sm btn-outline-info" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editStudent('${student.id}')" class="btn btn-sm btn-outline-primary" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteStudent('${student.id}', '${studentName}')" class="btn btn-sm btn-outline-danger" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 班级管理相关的模态框已删除

function renderStudentManagementModals() {
    return `
        <div class="modal fade" id="addStudentModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title"><i class="fas fa-user-plus"></i> 添加学生</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addStudentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="studentName" class="form-label">学生姓名 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="studentName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="studentId" class="form-label">学号</label>
                                        <input type="text" class="form-control" id="studentId" placeholder="留空自动生成">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentSchool" class="form-label">学校 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="studentSchool" required>
                                            <option value="">请选择学校</option>
                                            ${(getVisibleSchools(teacherSchools, teacherClasses) || []).map(school =>
                                                `<option value="${school.id}">${school.name}</option>`
                                            ).join('') || '<option value="">暂无可见学校</option>'}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentGrade" class="form-label">年级 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="studentGrade" required>
                                            <option value="">请选择年级</option>
                                            ${Array.from({length: 6}, (_, i) => i + 1).map(grade =>
                                                `<option value="${grade}">${grade}年级</option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentClass" class="form-label">班级 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="studentClass" required>
                                            <option value="">请选择班级</option>
                                            <!-- 班级选项将根据年级配置动态生成 -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentGender" class="form-label">性别</label>
                                        <select class="form-select" id="studentGender">
                                            <option value="">请选择性别</option>
                                            <option value="男">男</option>
                                            <option value="女">女</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentGroup" class="form-label">组号</label>
                                        <input type="number" class="form-control" id="studentGroup" min="1" placeholder="可选，如第1组">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <!-- 预留空间 -->
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-success" onclick="submitAddStudent()">添加学生</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// ==================== 事件处理函数 ====================

// 删除重复的showAddSchoolModal函数，使用上面的版本

/**
 * 显示编辑学校模态框
 */
function showEditSchoolModal(id, name, address = '', phone = '', email = '') {
    currentSchoolId = id;
    document.getElementById('editSchoolName').value = name;
    if (document.getElementById('editSchoolAddress')) document.getElementById('editSchoolAddress').value = address;
    if (document.getElementById('editSchoolPhone')) document.getElementById('editSchoolPhone').value = phone;
    if (document.getElementById('editSchoolEmail')) document.getElementById('editSchoolEmail').value = email;
    const modal = new bootstrap.Modal(document.getElementById('editSchoolModal'));
    modal.show();
}

/**
 * 查看学校详情
 */
function viewSchoolDetails(schoolId) {
    const school = teacherSchools.find(s => s.id == schoolId);
    if (!school) {
        showMessage('学校信息不存在', 'error');
        return;
    }
    showMessage(`学校详情：${school.name}`, 'info');
}

/**
 * 刷新学校数据
 */
async function refreshSchoolData() {
    console.log('刷新学校数据...');
    teacherSchools = await loadTeacherSchoolsFromAPI();
    if (currentSection === 'school-management') {
        showSchoolManagement();
    } else if (currentSection === 'dashboard') {
        showTeacherSection('dashboard');
    }
    console.log('学校数据刷新完成');
}

/**
 * 显示年级班级配置模态框
 */
async function showGradeClassConfig(schoolId, grade) {
    const school = teacherSchools.find(s => s.id == schoolId);
    if (!school) {
        showMessage('学校信息不存在', 'error');
        return;
    }

    // 教师端不再维护年级总班级数，仅提示
    const modalContent = document.getElementById('gradeClassConfigContent');
    modalContent.innerHTML = `
        <div class="text-center mb-3">
            <h6>${school.name} - ${grade}年级班级配置</h6>
            <p class="text-muted">通过点击选择具体班级来配置，已选数量即为该年级的班级总数</p>
        </div>
        <div class="mb-2 d-flex align-items-center justify-content-between">
            <div>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="gc-select-all">全选</button>
                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" id="gc-clear-all">清空</button>
            </div>
            <div class="text-muted">已选：<span id="gc-selected-count">0</span> 个班</div>
        </div>
        <div id="gc-chips" class="d-flex flex-wrap gap-2">
            <!-- 动态生成班级Chip -->
        </div>
        <div id="loadingConfig" class="text-muted mt-2">
            <i class="fas fa-info-circle"></i> 年级总班级数由管理员维护。教师端不再设置总班级数。
        </div>
    `;

    // 直接显示提示，不再生成Chip/保存事件
    const modal = new bootstrap.Modal(document.getElementById('gradeClassConfigModal'));
    modal.show();

// updateGradeBadges 函数已移至文件顶部

// refreshAllGradeBadges 函数已移至文件顶部

// =============== 任教班级设置（checkbox树形） ===============
// 旧版“整校树形任教设置”模态框已移除
// 选择任教班级（限定在某个年级范围内）
async function showSelectTeachingClasses(schoolId, grade) {
    try {
        // 拉取该学校已配置的总班级数
        const resp = await fetch(`/api/teacher/schools/${schoolId}/grades`, {
            headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        });
        const json = await resp.json();
        if (!resp.ok || !json.success) {
            showMessage('加载年级配置失败', 'error');
            return;
        }
        const cfg = (json.data || []).find(c => String(c.grade) === String(grade));
        const total = cfg ? parseInt(cfg.class_count) || 0 : 0;

        // 拉取当前教师在该学校的任教班级权限
        const permResp = await fetch(`/api/teacher/classes/permissions?school_id=${schoolId}`, {
            headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        });
        const permJson = await permResp.json();
        const currentPerms = (permResp.ok && permJson.success) ? (permJson.data || []) : [];
        const mySet = new Set(currentPerms
            .filter(p => String(p.grade) === String(grade))
            .map(p => `${p.school_id}-${p.grade}-${p.class}`));

        // 组装UI
        const list = Array.from({length: total}, (_, i) => i + 1).map(cls => {
            const key = `${schoolId}-${grade}-${cls}`;
            const checked = mySet.has(key) ? 'checked' : '';
            return `<div class="form-check ms-1">
                <input class="form-check-input stc-node" type="checkbox" data-school="${schoolId}" data-grade="${grade}" data-class="${cls}" id="stc-${key}" ${checked}>
                <label class="form-check-label" for="stc-${key}">${grade}年级${cls}班</label>
            </div>`;
        }).join('') || '<div class="text-muted">该年级未配置班级</div>';

        const html = `
        <div class="modal fade" id="selectTeachingClassesModal" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-tasks"></i> 选择任教班级（${grade}年级）</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <div>
                    <button class="btn btn-sm btn-outline-secondary" id="stc-select-all">全选</button>
                    <button class="btn btn-sm btn-outline-secondary ms-2" id="stc-clear-all">清空</button>
                  </div>
                  <div class="text-muted">已选：<span id="stc-count">0</span> 个班</div>
                </div>
                <div id="stc-list" class="d-grid" style="grid-template-columns: repeat(2, 1fr); gap: 6px;">${list}</div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" id="stc-save">保存</button>
              </div>
            </div>
          </div>
        </div>`;

        // 注入并显示
        const holder = document.createElement('div');
        holder.innerHTML = html;
        document.body.appendChild(holder);
        const modalEl = holder.querySelector('#selectTeachingClassesModal');
        const modal = new bootstrap.Modal(modalEl);

        // 计数
        const countEl = holder.querySelector('#stc-count');
        const listEl = holder.querySelector('#stc-list');
        const updateCount = () => {
          countEl.textContent = listEl.querySelectorAll('.stc-node:checked').length;
        };
        updateCount();
        listEl.addEventListener('change', (e) => {
          if (e.target && e.target.classList.contains('stc-node')) updateCount();
        });
        holder.querySelector('#stc-select-all').addEventListener('click', () => {
          listEl.querySelectorAll('.stc-node').forEach(i => i.checked = true);
          updateCount();
        });
        holder.querySelector('#stc-clear-all').addEventListener('click', () => {
          listEl.querySelectorAll('.stc-node').forEach(i => i.checked = false);
          updateCount();
        });

        holder.querySelector('#stc-save').addEventListener('click', async () => {
          const checked = Array.from(listEl.querySelectorAll('.stc-node:checked')).map(chk => ({
            school_id: parseInt(chk.getAttribute('data-school')),
            grade: parseInt(chk.getAttribute('data-grade')),
            class: parseInt(chk.getAttribute('data-class'))
          }));
          try {
            const resp = await fetch('/api/teacher/classes/permissions', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${localStorage.getItem('token')}` },
              body: JSON.stringify({ classes: checked, school_id: parseInt(schoolId) })
            });
            const result = await resp.json();
            if (!resp.ok || !result.success) throw new Error(result.message || '保存失败');
            showMessage('任教班级已更新', 'success');
            modal.hide();
            holder.remove();
            // 刷新我的任教班级与控制台统计
            teacherClasses = await loadTeacherClassesFromAPI();
            if (currentSection === 'dashboard') showTeacherDashboard();
          } catch (e) {
            console.error(e);
            showMessage('保存失败，请重试', 'error');
          }
        });

        modalEl.addEventListener('hidden.bs.modal', () => holder.remove());
        modal.show();
    } catch (e) {
        console.error('显示“选择任教班级”失败:', e);
        showMessage('显示“选择任教班级”失败', 'error');
    }
}



/**
 * 保存年级班级配置
 */
async function saveGradeClassConfig() {
    // 教师端不再提交年级总班级数，此函数保留避免旧引用报错，仅提示并关闭
    showMessage('年级总班级数由管理员维护，教师端不需设置', 'info');
    const modalEl = document.getElementById('gradeClassConfigModal');
    if (modalEl) {
        const modalInstance = (bootstrap.Modal.getInstance && bootstrap.Modal.getInstance(modalEl)) || new bootstrap.Modal(modalEl);
        modalInstance.hide();
    }
    return;

    // 旧实现已彻底移除
}

/**
 * 下载Excel模板
 */
function downloadExcelTemplate() {
    try {
        // 检查XLSX是否可用
        if (typeof XLSX === 'undefined') {
            // 未加载 Excel 库，直接生成 CSV 模板作为兜底
            try {
                const csvHeader = '学校名称,年级,班级,学生姓名,学号,性别,组号,备注';
                const csvRows = [
                    '示例小学,1,1,张三,20240001,男,1,',
                    '示例小学,1,1,李四,20240002,女,1,',
                    '示例小学,1,2,王五,20240003,男,2,',
                    '示例中学,7,1,赵六,20247001,女,,'
                ];
                const csvContent = [csvHeader, ...csvRows].join('\n');
                const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `学生导入模板_${new Date().toISOString().slice(0, 10)}.csv`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
                showMessage('未检测到Excel库，已下载CSV模板', 'warning');
            } catch (e) {
                console.error('生成CSV模板失败:', e);
                showMessage('模板生成失败，请检查浏览器兼容性', 'error');
            }
            return;
        }

        // 创建简化的模板数据，确保格式正确
        const templateData = [
            ['学校名称', '年级', '班级', '学生姓名', '学号', '性别', '备注'],
            ['示例小学', '1', '1', '张三', '20240001', '男', ''],
            ['示例小学', '1', '1', '李四', '20240002', '女', ''],
            ['示例小学', '1', '2', '王五', '20240003', '男', '转学生'],
            ['示例中学', '7', '1', '赵六', '20247001', '女', '']
        ];

        // 扩展模板：加入“组号”列
        templateData[0].splice(6, 0, '组号');
        templateData[1].splice(6, 0, '1');
        templateData[2].splice(6, 0, '1');
        templateData[3].splice(6, 0, '2');
        templateData[4].splice(6, 0, '');


        // 创建工作簿
        const wb = XLSX.utils.book_new();

        // 创建工作表
        const ws = XLSX.utils.aoa_to_sheet(templateData);

        // 设置列宽
        const colWidths = [
            { wch: 15 }, // 学校名称
            { wch: 8 },  // 年级
            { wch: 8 },  // 班级
            { wch: 12 }, // 学生姓名
            { wch: 12 }, // 学号
            { wch: 8 },  // 性别
            { wch: 20 }  // 备注
        ];
        ws['!cols'] = colWidths;

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '学生导入模板');

        // 生成文件并下载
        const fileName = `学生导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`;
        XLSX.writeFile(wb, fileName);

        showMessage('Excel模板下载成功', 'success');

    } catch (error) {
        console.error('生成Excel模板失败:', error);

        // 提供CSV格式的备用方案
        try {
            const csvContent = [
                '学校名称,年级,班级,学生姓名,学号,性别,备注',
                '示例小学,1,1,张三,20240001,男,',
                '示例小学,1,1,李四,20240002,女,',
                '示例小学,1,2,王五,20240003,男,转学生',
                '示例中学,7,1,赵六,20247001,女,'
            ].join('\n');

            // 创建CSV下载
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `学生导入模板_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            showMessage('已生成CSV格式模板', 'warning');
        } catch (csvError) {
            console.error('生成CSV模板也失败:', csvError);
            showMessage('模板生成失败，请检查浏览器兼容性', 'error');
        }
    }
}

/**
 * 显示增强的Excel导入模态框
 */
function showExcelImportModal() {
    const modalHtml = `
        <div class="modal fade" id="excelImportModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title"><i class="fas fa-file-excel"></i> Excel批量导入学生</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6>导入说明</h6>
                            <p class="mb-2">• 支持导入包含完整学校、年级、班级、学生信息的Excel文件</p>
                            <p class="mb-2">• 如果学校、年级、班级不存在，系统将自动创建</p>
                            <p class="mb-0">• 建议先下载模板，按格式填写后再导入</p>
                        </div>

                        <div class="file-upload-area border-2 border-dashed border-primary rounded p-4 text-center" id="fileUploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h6>拖拽Excel文件到此处或点击选择文件</h6>
                            <p class="text-muted mb-3">支持 .xlsx 和 .xls 格式</p>
                            <input type="file" id="excelFileInput" accept=".xlsx,.xls" style="display: none;">
                            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('excelFileInput').click()">
                                <i class="fas fa-folder-open"></i> 选择文件
                            </button>
                        </div>

                        <div id="importPreview" style="display: none;" class="mt-4">
                            <h6>导入预览</h6>
                            <div id="previewContent" class="border rounded p-3"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmImportBtn" onclick="confirmExcelImport()" disabled>
                            <i class="fas fa-upload"></i> 确认导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('excelImportModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定文件选择事件
    setupExcelFileUpload();

    // 显示模态框
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        const modal = new bootstrap.Modal(document.getElementById('excelImportModal'));
        modal.show();
    } else {
        console.warn('Bootstrap未加载，使用基础模态框显示');
        const modalElement = document.getElementById('excelImportModal');
        if (modalElement) {
            modalElement.style.display = 'block';
            modalElement.classList.add('show');
        }
    }
}

/**
 * 设置Excel文件上传处理
 */
function setupExcelFileUpload() {
    const fileInput = document.getElementById('excelFileInput');
    const uploadArea = document.getElementById('fileUploadArea');

    fileInput.addEventListener('change', handleExcelFileSelect);

    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('border-success');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('border-success');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('border-success');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleExcelFileSelect({ target: { files } });
        }
    });
}

/**
 * 处理Excel文件选择
 */
function handleExcelFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    console.log('选择的文件:', file);
    console.log('文件类型:', file.type);
    console.log('文件大小:', file.size);

    const preview = document.getElementById('importPreview');
    const content = document.getElementById('previewContent');
    const confirmBtn = document.getElementById('confirmImportBtn');

    // 检查文件格式
    if (!file.name.match(/\.(xlsx|xls|csv)$/i)) {
        showMessage('请选择Excel文件（.xlsx、.xls格式）或CSV文件', 'error');
        return;
    }

    // 检查文件大小
    if (file.size === 0) {
        showMessage('文件为空，请选择有内容的文件', 'error');
        return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB限制
        showMessage('文件过大，请选择小于5MB的文件', 'error');
        return;
    }

    // 显示解析中状态
    content.innerHTML = `
        <div class="d-flex align-items-center justify-content-center p-4">
            <div class="spinner-border text-primary me-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>
                <h6 class="mb-1">正在解析文件...</h6>
                <small class="text-muted">${file.name} (${(file.size / 1024).toFixed(2)} KB)</small>
            </div>
        </div>
    `;

    preview.style.display = 'block';
    confirmBtn.disabled = true;

    // 首先检查XLSX库是否可用
    if (typeof XLSX === 'undefined') {
        content.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                Excel解析库未加载！请刷新页面重试。
                <br><br>
                <small>如果问题持续存在，请检查网络连接</small>
            </div>
        `;
        confirmBtn.disabled = true;
        return;
    }

    console.log('XLSX库版本:', XLSX.version);

    // 延迟解析文件，给UI时间更新
    setTimeout(() => {
        parseExcelFile(file)
            .then(parsedData => {
                console.log('文件解析成功:', parsedData);
                if (parsedData && parsedData.total > 0) {
                    displayImportPreview(parsedData, file.name);
                    confirmBtn.disabled = false;
                    window.parsedStudentData = parsedData;
                } else {
                    content.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            文件解析成功但没有找到有效数据
                            <br><br>
                            <strong>请检查：</strong>
                            <ul class="mb-0 mt-2">
                                <li>文件是否包含表头：学校名称、年级、班级、学生姓名</li>
                                <li>数据行是否填写完整</li>
                                <li>是否选择了正确的工作表</li>
                            </ul>
                        </div>
                    `;
                    confirmBtn.disabled = true;
                }
            })
            .catch(error => {
                console.error('文件解析失败:', error);
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle"></i>
                        <strong>文件解析失败</strong>
                        <br><br>
                        <strong>错误信息：</strong>${error.message}
                        <br><br>
                        <strong>解决建议：</strong>
                        <ul class="mb-0 mt-2">
                            <li>确保文件格式正确（.xlsx、.xls或.csv）</li>
                            <li>检查文件是否损坏</li>
                            <li>尝试重新下载模板并填写数据</li>
                            <li>确保文件不是只读或被其他程序占用</li>
                        </ul>
                    </div>
                `;
                confirmBtn.disabled = true;
            });
    }, 100);
}

/**
 * 解析Excel/CSV文件
 */
function parseExcelFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = function(e) {
            try {
                let data;

                if (file.name.match(/\.csv$/i)) {
                    // CSV文件解析
                    const csvText = e.target.result;
                    data = parseCSV(csvText);
                } else {
                    // Excel文件解析
                    if (typeof XLSX === 'undefined') {
                        reject(new Error('Excel解析库未加载，请刷新页面重试'));
                        return;
                    }

                    try {
                        const arrayBuffer = e.target.result;
                        const workbook = XLSX.read(arrayBuffer, {
                            type: 'array',
                            cellDates: true,
                            cellNF: false,
                            cellText: false
                        });

                        if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                            reject(new Error('Excel文件中没有找到工作表'));
                            return;
                        }

                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];

                        if (!worksheet) {
                            reject(new Error('无法读取Excel工作表'));
                            return;
                        }

                        // 使用json格式先解析，然后转换为数组格式
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                            header: 1,
                            raw: false,
                            defval: ''
                        });

                        if (!jsonData || jsonData.length === 0) {
                            reject(new Error('Excel文件为空或无法解析'));
                            return;
                        }

                        data = jsonData;
                        console.log('Excel解析结果:', data);

                    } catch (xlsxError) {
                        console.error('XLSX解析错误:', xlsxError);
                        reject(new Error(`Excel文件格式错误: ${xlsxError.message}`));
                        return;
                    }
                }

                // 处理解析后的数据
                if (!data || data.length === 0) {
                    reject(new Error('文件内容为空'));
                    return;
                }

                console.log('原始数据:', data);
                const processedData = processImportData(data);
                resolve(processedData);

            } catch (error) {
                console.error('文件处理错误:', error);
                reject(new Error(`文件处理失败: ${error.message}`));
            }
        };

        reader.onerror = function(error) {
            console.error('文件读取错误:', error);
            reject(new Error('文件读取失败'));
        };

        // 根据文件类型选择读取方式
        if (file.name.match(/\.csv$/i)) {
            reader.readAsText(file, 'UTF-8');
        } else {
            reader.readAsArrayBuffer(file); // 使用ArrayBuffer而不是BinaryString
        }
    });
}

/**
 * 解析CSV文本
 */
function parseCSV(csvText) {
    try {
        const lines = csvText.split('\n');
        const result = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line) {
                // 处理CSV中的引号和逗号
                const row = [];
                let current = '';
                let inQuotes = false;

                for (let j = 0; j < line.length; j++) {
                    const char = line[j];

                    if (char === '"' && !inQuotes) {
                        inQuotes = true;
                    } else if (char === '"' && inQuotes) {
                        // 检查下一个字符是否也是引号（转义引号）
                        if (j + 1 < line.length && line[j + 1] === '"') {
                            current += '"';
                            j++; // 跳过下一个引号
                        } else {
                            inQuotes = false;
                        }
                    } else if (char === ',' && !inQuotes) {
                        row.push(current.trim());
                        current = '';
                    } else {
                        current += char;
                    }
                }

                // 添加最后一列
                row.push(current.trim());

                // 过滤空行
                if (row.some(cell => cell && cell.trim())) {
                    result.push(row);
                }
            }
        }

        console.log('CSV解析结果:', result);
        return result;

    } catch (error) {
        console.error('CSV解析错误:', error);
        throw new Error(`CSV解析失败: ${error.message}`);
    }
}

/**
 * 处理导入数据
 */
function processImportData(rawData) {
    console.log('开始处理导入数据:', rawData);

    if (!rawData || rawData.length < 2) {
        throw new Error('文件数据为空或格式不正确');
    }

    const headers = rawData[0];
    const dataRows = rawData.slice(1);

    console.log('表头:', headers);
    console.log('数据行数:', dataRows.length);

    // 查找必需列的索引
    const columnMap = {};
    const requiredColumns = ['学校名称', '年级', '班级', '学生姓名'];
    const optionalColumns = ['学号', '性别', '组号', '备注'];

    headers.forEach((header, index) => {
        const cleanHeader = header.toString().trim();
        console.log(`列 ${index}: "${cleanHeader}"`);
        if (requiredColumns.includes(cleanHeader) || optionalColumns.includes(cleanHeader)) {
            columnMap[cleanHeader] = index;
            console.log(`匹配到必需列: ${cleanHeader} -> 索引 ${index}`);
        }
    });

    console.log('列映射:', columnMap);

    // 检查必需列是否存在
    const missingColumns = requiredColumns.filter(col => !(col in columnMap));
    if (missingColumns.length > 0) {
        console.error('缺少必需的列:', missingColumns);
        throw new Error(`缺少必需的列：${missingColumns.join('、')}\n\n当前表头：${headers.join('、')}\n\n请确保Excel文件包含以下列：${requiredColumns.join('、')}`);
    }

    // 处理数据行
    const processedStudents = [];
    const errors = [];

    dataRows.forEach((row, index) => {
        const rowNumber = index + 2; // Excel行号（从2开始，因为第1行是标题）

        try {
            console.log(`处理第${rowNumber}行:`, row);

            // 跳过空行
            if (!row || row.every(cell => !cell || cell.toString().trim() === '')) {
                console.log(`跳过空行 ${rowNumber}`);
                return;
            }

            const schoolName = row[columnMap['学校名称']]?.toString().trim();
            const gradeStr = row[columnMap['年级']]?.toString().trim();
            const classStr = row[columnMap['班级']]?.toString().trim();
            const studentName = row[columnMap['学生姓名']]?.toString().trim();
            const studentId = columnMap['学号'] !== undefined ?
                row[columnMap['学号']]?.toString().trim() : '';
            const gender = columnMap['性别'] !== undefined ?
                row[columnMap['性别']]?.toString().trim() : '';
            const groupStr = columnMap['组号'] !== undefined ?
                row[columnMap['组号']]?.toString().trim() : '';
            const remarks = columnMap['备注'] !== undefined ?
                row[columnMap['备注']]?.toString().trim() : '';

            console.log(`第${rowNumber}行数据:`, {
                schoolName, gradeStr, classStr, studentName, studentId, gender, remarks
            });

            // 验证必需字段
            if (!schoolName) {
                errors.push(`第${rowNumber}行：学校名称不能为空`);
                return;
            }
            if (!studentName) {
                errors.push(`第${rowNumber}行：学生姓名不能为空`);
                return;
            }

            // 解析年级和班级（更宽松的验证）
            let grade = parseInt(gradeStr);
            let className = parseInt(classStr);

            if (isNaN(grade) || grade < 1 || grade > 15) { // 扩大年级范围
                errors.push(`第${rowNumber}行：年级必须是1-15的数字，当前值：${gradeStr}`);
                return;
            }
            if (isNaN(className) || className < 1 || className > 99) { // 扩大班级范围
                errors.push(`第${rowNumber}行：班级必须是1-99的数字，当前值：${classStr}`);
                return;
            }

            const studentData = {
                schoolName,
                grade,
                class: className,
                studentName,
                studentId,
                gender,
                group_number: (groupStr && !isNaN(parseInt(groupStr))) ? parseInt(groupStr) : undefined,
                remarks,
                rowNumber
            };

            console.log(`第${rowNumber}行处理成功:`, studentData);
            processedStudents.push(studentData);

        } catch (error) {
            console.error(`第${rowNumber}行处理错误:`, error);
            errors.push(`第${rowNumber}行：数据格式错误 - ${error.message}`);
        }
    });

    console.log('处理结果:', {
        成功: processedStudents.length,
        错误: errors.length,
        错误详情: errors
    });

    if (errors.length > 0 && processedStudents.length === 0) {
        throw new Error(`数据验证失败：\n${errors.join('\n')}`);
    }

    return {
        students: processedStudents,
        errors: errors,
        total: processedStudents.length
    };
}

/**
 * 显示导入预览
 */
function displayImportPreview(parsedData, fileName) {
    const content = document.getElementById('previewContent');
    const { students, errors, total } = parsedData;

    let previewHtml = `
        <div class="d-flex align-items-center justify-content-between mb-3">
            <div>
                <h6 class="mb-1">
                    <i class="fas fa-file-excel text-success"></i> ${fileName}
                </h6>
                <small class="text-muted">解析完成，共发现 ${total} 条有效学生记录</small>
            </div>
            <div>
                <span class="badge bg-success">${total} 条记录</span>
            </div>
        </div>
    `;

    // 显示错误信息（如果有）
    if (errors.length > 0) {
        previewHtml += `
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> 发现 ${errors.length} 个警告：</h6>
                <div class="small" style="max-height: 100px; overflow-y: auto;">
                    ${errors.slice(0, 5).map(error => `<div>• ${error}</div>`).join('')}
                    ${errors.length > 5 ? `<div class="text-muted">... 还有 ${errors.length - 5} 个警告</div>` : ''}
                </div>
            </div>
        `;
    }

    // 统计信息
    const schoolStats = {};
    const gradeStats = {};

    students.forEach(student => {
        schoolStats[student.schoolName] = (schoolStats[student.schoolName] || 0) + 1;
        const gradeKey = `${student.grade}年级`;
        gradeStats[gradeKey] = (gradeStats[gradeKey] || 0) + 1;
    });

    previewHtml += `
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">学校分布</h6>
                        ${Object.entries(schoolStats).map(([school, count]) =>
                            `<div class="d-flex justify-content-between">
                                <span>${school}</span>
                                <span class="badge bg-primary">${count}人</span>
                            </div>`
                        ).join('')}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">年级分布</h6>
                        ${Object.entries(gradeStats).map(([grade, count]) =>
                            `<div class="d-flex justify-content-between">
                                <span>${grade}</span>
                                <span class="badge bg-info">${count}人</span>
                            </div>`
                        ).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;

    // 预览前几条数据
    if (students.length > 0) {
        previewHtml += `
            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                <table class="table table-sm table-striped">
                    <thead class="table-light sticky-top">
                        <tr>
                            <th>学校</th>
                            <th>年级</th>
                            <th>班级</th>
                            <th>姓名</th>
                            <th>学号</th>
                            <th>性别</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${students.slice(0, 10).map(student => `
                            <tr>
                                <td><small>${student.schoolName}</small></td>
                                <td><small>${student.grade}</small></td>
                                <td><small>${student.class}</small></td>
                                <td><small><strong>${student.studentName}</strong></small></td>
                                <td><small>${student.studentId || '自动生成'}</small></td>
                                <td><small>${student.gender || '-'}</small></td>
                            </tr>
                        `).join('')}
                        ${students.length > 10 ?
                            `<tr><td colspan="6" class="text-center text-muted">... 还有 ${students.length - 10} 条记录</td></tr>` :
                            ''
                        }
                    </tbody>
                </table>
            </div>
        `;
    }

    content.innerHTML = previewHtml;
}
/**
 * 确认Excel导入
 */
async function confirmExcelImport() {
    const parsedData = window.parsedStudentData;
    if (!parsedData || !parsedData.students || parsedData.students.length === 0) {
        showMessage('没有可导入的数据', 'error');
        return;
    }

    const confirmBtn = document.getElementById('confirmImportBtn');
    const originalText = confirmBtn.innerHTML;

    try {
        // 显示导入进度
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在导入...';
        confirmBtn.disabled = true;

        const { students } = parsedData;
        const totalStudents = students.length;
        let successCount = 0;
        let errorCount = 0;
        const importErrors = [];


        // 将“组号”字段透传给每个学生的导入任务
        students.forEach(s => {
            if (s && (s['组号'] !== undefined && String(s['组号']).trim() !== '')) {
                const v = parseInt(s['组号']);
                if (!isNaN(v)) s.group_number = v;
            }
        });

        // 显示进度条
        const previewContent = document.getElementById('previewContent');
        if (previewContent) {
            const progressHtml = `
                <div class="mt-3" id="import-progress-container">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small>导入进度</small>
                        <small id="import-progress-text">0/${totalStudents}</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div id="import-progress-bar" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="mt-2">
                        <small id="import-status-text">准备开始导入...</small>
                    </div>
                </div>
            `;
            previewContent.insertAdjacentHTML('beforeend', progressHtml);
        }

        console.log(`开始批量导入 ${totalStudents} 个学生`);

        // 批量导入学生（分批处理，避免一次性请求过多）
        const batchSize = 10;
        for (let i = 0; i < students.length; i += batchSize) {
            const batch = students.slice(i, i + batchSize);
            console.log(`处理批次 ${Math.floor(i/batchSize) + 1}, 学生: ${batch.map(s => s.studentName).join(', ')}`);

            const batchResults = await Promise.allSettled(
                batch.map(student => importSingleStudent(student))
            );

            // 处理批次结果
            batchResults.forEach((result, index) => {
                const studentIndex = i + index;
                const student = batch[index];

                if (result.status === 'fulfilled') {
                    successCount++;
                    console.log(`成功导入: ${student.studentName}`);
                } else {
                    errorCount++;
                    const errorMsg = result.reason?.message || '未知错误';
                    console.error(`导入失败: ${student.studentName} - ${errorMsg}`);
                    importErrors.push({
                        student: `${student.studentName} (${student.schoolName} ${student.grade}年级${student.class}班)`,
                        error: errorMsg
                    });
                }

                // 更新进度 - 安全地更新DOM元素
                const progress = Math.round(((studentIndex + 1) / totalStudents) * 100);
                const progressBar = document.getElementById('import-progress-bar');
                const progressText = document.getElementById('import-progress-text');
                const statusText = document.getElementById('import-status-text');

                if (progressBar) progressBar.style.width = `${progress}%`;
                if (progressText) progressText.textContent = `${studentIndex + 1}/${totalStudents}`;
                if (statusText) statusText.textContent = `正在导入: ${student.studentName}`;
            });

            // 短暂延迟，避免请求过快
            if (i + batchSize < students.length) {
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        // 显示导入结果
        let resultMessage = `导入完成！成功：${successCount} 条，失败：${errorCount} 条`;
        let resultType = 'success';

        if (errorCount > 0) {
            resultType = successCount > 0 ? 'warning' : 'error';
            if (importErrors.length <= 3) {
                resultMessage += `\n\n失败详情：\n${importErrors.map(err =>
                    `• ${err.student}: ${err.error}`
                ).join('\n')}`;
            } else {
                resultMessage += `\n\n部分失败详情：\n${importErrors.slice(0, 3).map(err =>
                    `• ${err.student}: ${err.error}`
                ).join('\n')}\n... 还有 ${importErrors.length - 3} 个错误`;
            }
        }

        console.log('导入结果:', { successCount, errorCount, errors: importErrors });
        showMessage(resultMessage, resultType);

        // 关闭模态框
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('excelImportModal'));
            if (modal) {
                modal.hide();
            }
        } else {
            // 降级方案：直接隐藏模态框
            const modalElement = document.getElementById('excelImportModal');
            if (modalElement) {
                modalElement.style.display = 'none';
                modalElement.classList.remove('show');
            }
        }

        // 如果有成功导入的数据，静默刷新学生列表
        if (successCount > 0) {
            try {
                console.log('Excel导入成功，静默刷新学生列表...');
                const updatedStudents = await refreshStudentDataSilently();

                if (updatedStudents) {
                    showMessage(`导入完成！成功导入 ${successCount} 个学生，列表已更新`, 'success');
                } else {
                    showMessage('导入成功但刷新列表失败，请手动刷新页面', 'warning');
                }
            } catch (refreshError) {
                console.error('刷新学生数据失败:', refreshError);
                showMessage('导入成功但刷新列表失败，请手动刷新页面', 'warning');
            }
        }

    } catch (error) {
        console.error('批量导入失败:', error);
        showMessage('导入过程中发生错误，请重试', 'error');
    } finally {
        // 恢复按钮状态
        if (confirmBtn) {
            confirmBtn.innerHTML = originalText;
            confirmBtn.disabled = false;
        }
    }
}

/**
 * 导入单个学生
 */
async function importSingleStudent(studentData) {
    const { schoolName, grade, class: className, studentName, studentId, gender, group_number: groupNumber, remarks } = studentData;

    try {
        // 首先确保学校存在并获取school_id
        const schoolInfo = await ensureSchoolExists(schoolName);
        const schoolId = schoolInfo.id;

        console.log(`准备导入学生: ${studentName}, 学校: ${schoolName}, 学校ID: ${schoolId}`);

        // 若拿到 schoolId，则确保年级配置满足所需班级数（自动创建/扩容）
        const needClassNo = parseInt(className) || 1;
        if (schoolId) {
            try { await ensureGradeConfig(parseInt(schoolId), parseInt(grade), needClassNo); } catch (e) { console.warn('ensureGradeConfig 失败', e); }
        }

        // 根据是否有school_id使用不同的策略
        let requestBody;
        if (schoolId) {
            // 有school_id，使用标准的teacher API
            requestBody = {
                name: studentName,                    // 后端期望 name
                student_id: studentId || null,       // 后端期望 student_id
                school_id: parseInt(schoolId),       // 后端期望 school_id (数字)
                grade: parseInt(grade),              // 后端期望 grade (数字)
                class: parseInt(className),          // 后端期望 class (数字)
                gender: gender || null,              // 可选字段
                group_number: (groupNumber !== undefined && groupNumber !== null) ? parseInt(groupNumber) : null,
                remarks: remarks || null             // 可选字段
            };
        } else {
            // 没有school_id，跳过权限检查（管理员模式或使用学校名称）
            requestBody = {
                name: studentName,
                student_id: studentId || null,
                school_name: schoolName,  // 使用学校名称而不是ID
                grade: parseInt(grade),
                class: parseInt(className),
                gender: gender || null,
                remarks: remarks || null
            };
        }

        console.log('发送请求数据:', requestBody);

        const response = await fetch('/api/teacher/students', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(requestBody)
        });

        const result = await response.json();
        console.log(`导入学生"${studentName}"响应:`, result);

        if (!response.ok) {
            // 如果是权限问题且用户是管理员，尝试使用管理员API
            if (response.status === 403 && result.message.includes('权限')) {
                console.log(`权限不足，尝试使用管理员API导入: ${studentName}`);
                return await importStudentAsAdmin(studentData, schoolInfo);
            }

            throw new Error(result.message || result.error || '导入失败');
        }

        return result;

    } catch (error) {
        console.error(`导入学生"${studentName}"失败:`, error);
        throw new Error(`导入学生"${studentName}"失败: ${error.message}`);
    }
}

/**
 * 使用管理员API导入学生
 */
async function importStudentAsAdmin(studentData, schoolInfo) {
    const { schoolName, grade, class: className, studentName, studentId, gender, remarks } = studentData;

    console.log(`尝试使用管理员API导入学生: ${studentName}`);

    try {
        // 使用admin API或直接插入
        const response = await fetch('/api/admin/students', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                name: studentName,
                student_identifier: studentId || `STU_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                grade: parseInt(grade),
                class: parseInt(className),
                school_id: schoolInfo.id ? parseInt(schoolInfo.id) : null,
                school_name: schoolName,
                gender: gender || null,
                remarks: remarks || null
            })
        });

        if (!response.ok) {
            throw new Error('管理员API也无法导入');
        }

        return await response.json();

    } catch (adminError) {
        console.error(`管理员API导入失败:`, adminError);
        throw new Error(`无法导入学生"${studentName}": ${adminError.message}`);
    }
}

/**
 * 确保学校存在，如果不存在则创建
 */
async function ensureSchoolExists(schoolName) {
    console.log(`检查学校是否存在: ${schoolName}`);
    if (!schoolName) throw new Error('缺少学校名称');

    // 先从本地缓存查找
    try {
        const existingSchool = (Array.isArray(teacherSchools) ? teacherSchools : []).find(s => s && s.name === schoolName);
        if (existingSchool && existingSchool.id) {
            console.log(`学校已在缓存中: ${schoolName}, ID: ${existingSchool.id}`);
            return existingSchool;
        }
    } catch (_) {}

    // 后端创建学校
    try {
        const resp = await authFetch(`/api/teacher/schools?ts=${Date.now()}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: schoolName })
        });
        let json = {};
        try { json = await resp.json(); } catch (_) {}
        if (!resp.ok || (json && json.success === false)) {
            throw new Error((json && (json.message || json.error)) || '创建学校失败');
        }
        const created = json && (json.data || json);
        // 刷新本地缓存
        try { teacherSchools = await loadTeacherSchoolsFromAPI(); } catch (e) { console.warn('刷新学校缓存失败', e); }
        console.log('学校已创建/确认存在:', created);
        return created;
    } catch (error) {
        console.error('ensureSchoolExists 失败:', error);
        // 兜底返回仅含名称的对象，后续可能走管理员导入
        return { name: schoolName, id: null };
    }
}

// 确保指定学校的年级配置满足所需班级数（不存在则创建/小于则扩容）
async function ensureGradeConfig(schoolId, grade, requiredClassNo = 1) {
    if (!schoolId || !grade) throw new Error('缺少学校或年级信息');
    const ts = Date.now();
    let list = [];
    try {
        const resp = await authFetch(`/api/teacher/schools/${schoolId}/grades?ts=${ts}`, {});
        let resj = {};
        try { resj = await resp.json(); } catch (_) {}
        list = Array.isArray(resj) ? resj : (Array.isArray(resj.data) ? resj.data : []);
    } catch (e) { list = []; }

    const need = Math.max(parseInt(requiredClassNo) || 1, 1);
    const cfg = list.find(c => String(c.grade) === String(grade));
    const current = cfg ? (parseInt(cfg.class_count) || 0) : 0;
    if (!cfg || current < need) {
        const post = await authFetch(`/api/teacher/schools/${schoolId}/grades/${grade}`, {
            method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ class_count: need })
        });
        let pj = {};
        try { pj = await post.json(); } catch (_) {}
        if (!post.ok || (pj && pj.success === false)) {
            throw new Error((pj && (pj.message || pj.error)) || '年级配置更新失败');
        }
        console.log(`年级配置已更新: 学校${schoolId} 年级${grade} => class_count=${need}`);
        return need;
    }
    return current;
}

// 打开“编辑学生”模态框
function openEditStudentModal(studentId) {
    const student = teacherStudents.find(s => String(s.id) === String(studentId));
    if (!student) {
        showMessage('未找到该学生', 'error');
        return;
    }

    const modalHtml = `
        <div class="modal fade" id="editStudentModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title"><i class="fas fa-edit"></i> 编辑学生</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editStudentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学生姓名 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="editStudentName" value="${student.name || ''}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学号</label>
                                        <input type="text" class="form-control" id="editStudentId" value="${student.student_identifier || ''}" placeholder="留空不修改">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">学校</label>
                                        <select class="form-select" id="editStudentSchool" disabled>
                                            ${teacherSchools.map(s => `<option value="${s.id}" ${String(s.id)===String(student.school_id)?'selected':''}>${s.name}</option>`).join('')}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">年级 <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="editStudentGrade" value="${student.grade || ''}" min="1" max="12" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">班级 <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="editStudentClass" value="${student.class || ''}" min="1" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">性别</label>
                                        <select class="form-select" id="editStudentGender">
                                            <option value="" ${!student.gender?'selected':''}>未设置</option>
                                            <option value="男" ${student.gender==='男'?'selected':''}>男</option>
                                            <option value="女" ${student.gender==='女'?'selected':''}>女</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">组号</label>
                                        <input type="number" class="form-control" id="editStudentGroup" value="${student.group_number || ''}" min="1">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitEditStudent('${student.id}')">保存</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    const existing = document.getElementById('editStudentModal');
    if (existing) existing.remove();
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('editStudentModal'));
    modal.show();
}

async function submitEditStudent(studentId) {
    const name = document.getElementById('editStudentName').value.trim();
    const studentIdentifier = document.getElementById('editStudentId').value.trim();
    const grade = document.getElementById('editStudentGrade').value;
    const studentClass = document.getElementById('editStudentClass').value;
    const gender = document.getElementById('editStudentGender').value;
    const groupNumber = document.getElementById('editStudentGroup').value;

    if (!name || !grade || !studentClass) {
        showMessage('请填写必填项', 'error');
        return;
    }

    const body = { name, grade: parseInt(grade), class: parseInt(studentClass) };
    if (studentIdentifier) body.student_id = studentIdentifier;
    if (gender) body.gender = gender;
    if (groupNumber) body.group_number = parseInt(groupNumber);

    try {
        const resp = await fetch(`/api/teacher/students/${studentId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(body)
        });
        const result = await resp.json();
        if (!resp.ok || !result.success) {
            throw new Error(result.message || '更新失败');
        }
        const modal = bootstrap.Modal.getInstance(document.getElementById('editStudentModal'));
        if (modal) modal.hide();
        teacherStudents = await loadTeacherStudentsFromAPI(true);
        if (currentSection === 'student-management') {
            showStudentManagement();
        }
        showMessage('学生信息已更新', 'success');
    } catch (e) {
        console.error('更新学生失败', e);
        showMessage(e.message, 'error');
    }
}


}

/**
 * 渲染增强的学生管理模态框
 */
function renderEnhancedStudentManagementModals() {
    return `
        <!-- 添加学生模态框 -->
        <div class="modal fade" id="addStudentModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title"><i class="fas fa-user-plus"></i> 添加学生</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addStudentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="studentName" class="form-label">学生姓名 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="studentName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="studentId" class="form-label">学号</label>
                                        <input type="text" class="form-control" id="studentId" placeholder="留空自动生成">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentSchool" class="form-label">学校 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="studentSchool" required onchange="loadSchoolGrades()">
                                            <option value="">请选择学校</option>
                                            ${(teacherSchools || []).map(school =>
                                                `<option value="${school.id}">${school.name}</option>`
                                            ).join('') || '<option value="">暂无学校</option>'}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentGrade" class="form-label">年级 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="studentGrade" required onchange="loadGradeClasses()">
                                            <option value="">请先选择学校</option>
                                            <!-- 年级选项将根据学校配置动态生成 -->
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentClass" class="form-label">班级 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="studentClass" required>
                                            <option value="">请选择班级</option>
                                            <!-- 班级选项将根据年级配置动态生成 -->
                                        </select>
                                    </div>
                                </div>

                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentGender" class="form-label">性别</label>
                                        <select class="form-select" id="studentGender">
                                            <option value="">请选择性别</option>
                                            <option value="男">男</option>
                                            <option value="女">女</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentGroup" class="form-label">组号</label>
                                        <input type="number" class="form-control" id="studentGroup" min="1" placeholder="可选，如第1组">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <!-- 预留空间 -->
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-success" onclick="submitAddStudent()">添加学生</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 绑定“添加学生”模态中的联动下拉（学校/年级→班级）
function bindStudentAddModalClassFill() {
    var schoolSelect = document.getElementById('studentSchool');
    var gradeSelect = document.getElementById('studentGrade');
    var classSelect = document.getElementById('studentClass');
    if (!schoolSelect || !gradeSelect || !classSelect) return;

    async function fillClasses() {
        var schoolId = schoolSelect && schoolSelect.value;
        var grade = gradeSelect && gradeSelect.value;
        if (!schoolId || !grade) {
            classSelect.innerHTML = '<option value="">请选择班级</option>';
            return;
        }
        try {
            // 先尝试获取配置
            let resp = await authFetch('/api/teacher/schools/' + schoolId + '/grades?ts=' + Date.now(), {});
            let result = resp && resp.ok ? await resp.json() : null;
            let list = result ? (Array.isArray(result) ? result : (Array.isArray(result.data) ? result.data : [])) : [];

            // 若该年级未配置或为0，则按需求自动创建为至少1班
            let cfg = list.find(function(c){ return String(c.grade) === String(grade); });
            let count = cfg ? parseInt(cfg.class_count) : 0;
            if (!cfg || !count) {
                try {
                    await ensureGradeConfig(parseInt(schoolId), parseInt(grade), 1);
                    // 再次获取最新配置
                    resp = await authFetch('/api/teacher/schools/' + schoolId + '/grades?ts=' + Date.now(), {});
                    result = resp && resp.ok ? await resp.json() : null;
                    list = result ? (Array.isArray(result) ? result : (Array.isArray(result.data) ? result.data : [])) : [];
                    cfg = list.find(function(c){ return String(c.grade) === String(grade); });
                    count = cfg ? parseInt(cfg.class_count) : 0;
                } catch (e) {
                    console.warn('自动创建年级配置失败', e);
                }
            }

            var opts = ['<option value="">' + (count ? '请选择班级' : '暂无班级') + '</option>'];
            for (var i = 1; i <= (count || 0); i++) { opts.push('<option value="' + i + '">' + i + '班</option>'); }
            classSelect.innerHTML = opts.join('');
        } catch (e) {
            console.warn('加载班级配置失败', e);
            classSelect.innerHTML = '<option value="">加载失败</option>';
        }
    }

    schoolSelect.addEventListener('change', fillClasses);
    gradeSelect.addEventListener('change', fillClasses);
    if (schoolSelect.value && gradeSelect.value) fillClasses();
}

/**
 * 为指定学校添加年级
 */
function showAddGradeForSchool(schoolId) {
    showMessage(`为学校${schoolId}添加年级功能开发中...`, 'info');
}

/**
 * 切换学校的年级配置
 */
function toggleGradeForSchool(schoolId, grade) {
    showMessage(`切换学校${schoolId}的${grade}年级配置功能开发中...`, 'info');
}

/**
 * 显示申请班级权限模态框
 */
function showApplyClassPermissionModal() {
    if (teacherSchools.length === 0) {
        showMessage('请先添加学校信息', 'warning');
        return;
    }
    const modal = new bootstrap.Modal(document.getElementById('applyClassPermissionModal'));
    modal.show();
}

/**
 * 提交班级权限申请
 */
function submitClassPermissionApplication() {
    const schoolId = document.getElementById('permissionSchool').value;
    const grade = document.getElementById('permissionGrade').value;
    const className = document.getElementById('permissionClass').value;

    if (!schoolId || !grade || !className) {
        showMessage('请完整填写申请信息', 'error');
        return;
    }
    showMessage('班级权限申请功能开发中...', 'info');
}

/**
 * 管理班级学生
 */
function manageClassStudents(schoolId, grade, className) {
    selectedSchool = schoolId;
    selectedGrade = grade;
    selectedClass = className;

    showTeacherSection('student-management');

    setTimeout(() => {
        const schoolSelect = document.getElementById('filterSchool');
        const gradeSelect = document.getElementById('filterGrade');
        const classSelect = document.getElementById('filterClass');

        if (schoolSelect) schoolSelect.value = schoolId;
        if (gradeSelect) gradeSelect.value = grade;
        if (classSelect) classSelect.value = className;

        updateStudentFilter();
    }, 100);
}

/**
 * 显示Excel导入模态框
 */
// editStudent函数已在上面定义，这里删除重复定义

// viewStudent函数已在上面定义，这里删除重复定义

/**
 * 删除学生
 */
async function deleteStudent(studentId, name) {
    // 显示确认对话框
    const confirmModal = document.createElement('div');
    confirmModal.className = 'modal fade';
    confirmModal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle"></i> 确认删除学生
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-3">您确定要删除学生 <strong>"${name}"</strong> 吗？</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>警告：</strong>此操作不可撤销，删除后将无法恢复学生数据！
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmSingleDelete('${studentId}', '${name}')">
                        <i class="fas fa-trash"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(confirmModal);
    const modal = new bootstrap.Modal(confirmModal);
    modal.show();

    // 模态框关闭时清理DOM
    confirmModal.addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 确认单个删除
async function confirmSingleDelete(studentId, name) {
    try {
        // 关闭确认对话框
        const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
        if (modal) modal.hide();

        // 显示加载状态在表格中
        const tbody = document.querySelector('#student-table-body');
        const originalContent = tbody.innerHTML;

        // 显示删除进度
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center p-4">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="spinner-border spinner-border-sm text-danger me-2" role="status"></div>
                        <span>正在删除学生 "${name}"...</span>
                    </div>
                </td>
            </tr>
        `;

        const response = await authFetch(`/api/teacher/students/${studentId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showMessage(`学生 "${name}" 删除成功`, 'success');

            // 快速刷新：直接从内存中移除学生，避免重新加载
            if (teacherStudents) {
                teacherStudents = teacherStudents.filter(s => String(s.id) !== String(studentId));
                tbody.innerHTML = renderStudentTableRows();

                // 更新统计信息
                updateFilterStats(teacherStudents.length, teacherStudents.length);

                // 更新学生数量显示
                const countElement = document.querySelector('h5');
                if (countElement && countElement.textContent.includes('学生列表')) {
                    countElement.innerHTML = `<i class="fas fa-users"></i> 学生列表 (${teacherStudents.length}人)`;
                }

                // 清除选择状态
                clearSelection();
            } else {
                // 如果内存中没有数据，则重新加载
                await refreshStudentData();
            }
        } else {
            // 恢复原始内容
            tbody.innerHTML = originalContent;
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || '删除失败');
        }
    } catch (error) {
        console.error('删除学生失败:', error);
        showMessage('删除学生失败: ' + error.message, 'error');

        // 恢复原始内容
        const tbody = document.querySelector('#student-table-body');
        if (tbody && teacherStudents) {
            tbody.innerHTML = renderStudentTableRows();
        }
    }
}

/**
 * 更新学生筛选
 */
function updateStudentFilter() {
    const schoolId = document.getElementById('filterSchool')?.value || '';
    const grade = document.getElementById('filterGrade')?.value || '';
    const classNo = document.getElementById('filterClass')?.value || '';
    const searchText = document.getElementById('searchStudent')?.value?.trim().toLowerCase() || '';

    if (!teacherStudents || teacherStudents.length === 0) {
        return;
    }

    // 筛选学生
    let filteredStudents = teacherStudents.filter(student => {
        // 学校筛选
        if (schoolId && String(student.school_id) !== schoolId) {
            return false;
        }

        // 年级筛选
        if (grade && String(student.grade || student.student_grade) !== grade) {
            return false;
        }

        // 班级筛选
        if (classNo && String(student.class || student.student_class) !== classNo) {
            return false;
        }

        // 搜索筛选（姓名、学号）
        if (searchText) {
            const studentName = (student.name || student.student_name || '').toLowerCase();
            const studentId = (student.student_id || student.student_identifier || '').toLowerCase();
            if (!studentName.includes(searchText) && !studentId.includes(searchText)) {
                return false;
            }
        }

        return true;
    });

    // 更新显示
    const tbody = document.querySelector('#student-table-body');
    if (tbody) {
        // 临时保存原始数据
        const originalStudents = teacherStudents;
        teacherStudents = filteredStudents;

        // 重新渲染表格
        tbody.innerHTML = renderStudentTableRows();

        // 恢复原始数据
        teacherStudents = originalStudents;

        // 更新统计信息
        updateFilterStats(filteredStudents.length, originalStudents.length);
    }
}

// 更新筛选统计信息
function updateFilterStats(filteredCount, totalCount) {
    const statsElement = document.getElementById('filterStats');
    if (statsElement) {
        if (filteredCount === totalCount) {
            statsElement.innerHTML = `<small class="text-muted">共 ${totalCount} 个学生</small>`;
        } else {
            statsElement.innerHTML = `<small class="text-info">筛选出 ${filteredCount} 个学生（共 ${totalCount} 个）</small>`;
        }
    }
}

// 处理搜索框回车键
function handleSearchKeyPress(event) {
    if (event.key === 'Enter') {
        updateStudentFilter();
    }
}

// 更新筛选器的年级选项
async function updateFilterGrades() {
    const schoolId = document.getElementById('filterSchool').value;
    const gradeSelect = document.getElementById('filterGrade');
    const classSelect = document.getElementById('filterClass');

    // 重置下级选择
    classSelect.innerHTML = '<option value="">请先选择年级</option>';

    if (!schoolId) {
        gradeSelect.innerHTML = '<option value="">请先选择学校</option>';
        return;
    }

    // 显示加载状态
    gradeSelect.innerHTML = '<option value="">正在加载年级...</option>';

    try {
        const response = await authFetch(`/api/teacher/schools/${schoolId}/grades`);
        if (response.ok) {
            const grades = await response.json();
            console.log('年级数据:', grades); // 调试信息

            if (Array.isArray(grades) && grades.length > 0) {
                const gradeOptions = grades.map(grade =>
                    `<option value="${grade.grade}">${grade.grade}年级</option>`
                ).join('');
                gradeSelect.innerHTML = '<option value="">全部年级</option>' + gradeOptions;
            } else {
                // 如果没有年级数据，显示默认年级
                let defaultGrades = '';
                for (let i = 1; i <= 6; i++) {
                    defaultGrades += `<option value="${i}">${i}年级</option>`;
                }
                gradeSelect.innerHTML = '<option value="">全部年级</option>' + defaultGrades;
            }
        } else {
            // 如果API失败，显示默认年级
            let defaultGrades = '';
            for (let i = 1; i <= 6; i++) {
                defaultGrades += `<option value="${i}">${i}年级</option>`;
            }
            gradeSelect.innerHTML = '<option value="">全部年级</option>' + defaultGrades;
        }
    } catch (error) {
        console.error('加载年级失败:', error);
        // 如果API失败，显示默认年级
        let defaultGrades = '';
        for (let i = 1; i <= 6; i++) {
            defaultGrades += `<option value="${i}">${i}年级</option>`;
        }
        gradeSelect.innerHTML = '<option value="">全部年级</option>' + defaultGrades;
    }
}

// 更新筛选器的班级选项
function updateFilterClasses() {
    const grade = document.getElementById('filterGrade').value;
    const classSelect = document.getElementById('filterClass');

    if (!grade) {
        classSelect.innerHTML = '<option value="">请先选择年级</option>';
        return;
    }

    // 生成1-20班的选项
    let classOptions = '';
    for (let i = 1; i <= 20; i++) {
        classOptions += `<option value="${i}">${i}班</option>`;
    }

    classSelect.innerHTML = '<option value="">全部班级</option>' + classOptions;
}

// 重置筛选条件
function resetStudentFilter() {
    const schoolSelect = document.getElementById('filterSchool');
    const gradeSelect = document.getElementById('filterGrade');
    const classSelect = document.getElementById('filterClass');
    const searchInput = document.getElementById('searchStudent');

    if (schoolSelect) schoolSelect.value = '';
    if (gradeSelect) {
        gradeSelect.innerHTML = '<option value="">请先选择学校</option>';
        gradeSelect.value = '';
    }
    if (classSelect) {
        classSelect.innerHTML = '<option value="">请先选择年级</option>';
        classSelect.value = '';
    }
    if (searchInput) searchInput.value = '';

    // 重新显示所有学生
    updateStudentFilter();
}
// 创建防抖版本的筛选函数，优化搜索性能
const debouncedUpdateStudentFilter = debounce(updateStudentFilter, 300);

// 重复函数已在上面定义，这里删除

/**
 * 重置学生筛选
 */
function resetStudentFilter() {
    const schoolSelect = document.getElementById('filterSchool');
    const gradeSelect = document.getElementById('filterGrade');
    const classSelect = document.getElementById('filterClass');
    const searchInput = document.getElementById('searchStudent');

    if (schoolSelect) schoolSelect.value = '';
    if (gradeSelect) {
        gradeSelect.innerHTML = '<option value="">全部年级</option>';
        gradeSelect.value = '';
    }
    if (classSelect) {
        classSelect.innerHTML = '<option value="">全部班级</option>';
        classSelect.value = '';
    }
    if (searchInput) searchInput.value = '';

    updateStudentFilter();
}



/**
 * 导出学生数据
 */
function exportStudentData() {
    showMessage('导出功能开发中...', 'info');
}

// refreshStudentData函数已在上面定义，这里删除重复定义

// 页面卸载时清理实时订阅
window.addEventListener('beforeunload', () => {
    console.log('页面卸载，清理实时订阅...');
    if (typeof SchoolRealtimeSync !== 'undefined') {
        SchoolRealtimeSync.stop();
    }
    if (typeof RealtimeManager !== 'undefined') {
        RealtimeManager.unsubscribeAll();
    }
});

// 导出到全局作用域
window.showTeacherSection = showTeacherSection;
window.showAddSchoolModal = showAddSchoolModal;

// 公开到全局的UI入口，避免未定义报错
window.showAddStudentModal = async function() {
    try {
        // 确保学校数据已加载
        if (!Array.isArray(teacherSchools) || teacherSchools.length === 0) {
            teacherSchools = await loadTeacherSchoolsFromAPI();
        }
        const existing = document.getElementById('addStudentModal');
        if (existing) existing.remove();
        document.body.insertAdjacentHTML('beforeend', renderEnhancedStudentManagementModals());

        // 检查bootstrap是否可用
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(document.getElementById('addStudentModal'));
            modal.show();
        } else {
            // 降级方案：直接显示模态框
            console.warn('Bootstrap未加载，使用基础模态框显示');
            const modalElement = document.getElementById('addStudentModal');
            if (modalElement) {
                modalElement.style.display = 'block';
                modalElement.classList.add('show');
                // 添加简单的backdrop
                const backdrop = document.createElement('div');
                backdrop.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:1040;';
                backdrop.onclick = function() {
                    modalElement.style.display = 'none';
                    modalElement.classList.remove('show');
                    backdrop.remove();
                };
                document.body.appendChild(backdrop);
            }
        }
        // 绑定联动填充（确保DOM已就绪）
        setTimeout(bindStudentAddModalClassFill, 0);
    } catch (e) {
        console.error('打开添加学生模态框失败:', e);
        showMessage('打开添加学生窗口失败，请刷新重试', 'error');
    }
};

window.submitAddSchool = submitAddSchool;
window.showEditSchoolModal = showEditSchoolModal;
window.submitEditSchool = submitEditSchool;
window.showDeleteSchoolModal = showDeleteSchoolModal;
window.showGradeClassConfig = showGradeClassConfig;

window.confirmDeleteSchool = confirmDeleteSchool;
window.refreshSchoolsList = refreshSchoolsList;
window.teacherLogout = teacherLogout;

// 确保downloadExcelTemplate函数存在
if (typeof downloadExcelTemplate === 'function') {
    window.downloadExcelTemplate = downloadExcelTemplate;
} else {
    window.downloadExcelTemplate = function() {
        console.error('downloadExcelTemplate函数未定义，使用备用方案');
        // 创建简单的CSV模板
        const csvContent = '学校名称,年级,班级,学生姓名,学号,性别,组号,备注\n示例小学,1,1,张三,20240001,男,1,示例数据';
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = '学生导入模板.csv';
        link.click();
    };
}

// 确保函数存在后再导出
if (typeof showExcelImportModal === 'function') {
    window.showExcelImportModal = showExcelImportModal;
} else {
    window.showExcelImportModal = function() {
        console.error('showExcelImportModal函数未定义');
        showMessage('Excel导入功能暂时不可用', 'error');
    };
}

if (typeof confirmExcelImport === 'function') {
    window.confirmExcelImport = confirmExcelImport;
} else {
    window.confirmExcelImport = function() {
        console.error('confirmExcelImport函数未定义');
    };
}

// 导出保存配置函数，确保在内联onclick可调用
window.saveGradeClassConfig = saveGradeClassConfig;

window.submitAddStudent = submitAddStudent;
window.openEditStudentModal = openEditStudentModal;
window.submitEditStudent = submitEditStudent;

// 导出新入口，保证内联onclick可用
window.showSelectTeachingClasses = showSelectTeachingClasses;

// 加载学校的年级配置
async function loadSchoolGrades() {
    const schoolSelect = document.getElementById('studentSchool');
    const gradeSelect = document.getElementById('studentGrade');
    const classSelect = document.getElementById('studentClass');

    if (!schoolSelect || !gradeSelect || !classSelect) return;

    const schoolId = schoolSelect.value;

    // 清空年级和班级选择
    gradeSelect.innerHTML = '<option value="">请选择年级</option>';
    classSelect.innerHTML = '<option value="">请先选择年级</option>';

    if (!schoolId) {
        gradeSelect.innerHTML = '<option value="">请先选择学校</option>';
        return;
    }

    try {
        // 显示加载状态
        gradeSelect.innerHTML = '<option value="">正在加载年级...</option>';

        // 获取学校的年级配置
        const response = await authFetch(`/api/teacher/schools/${schoolId}/grades`, {}, 2000);

        if (response.ok) {
            const result = await response.json();
            const grades = (result && (result.success === undefined || result.success === true))
                ? (Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []))
                : [];

            if (grades.length > 0) {
                gradeSelect.innerHTML = '<option value="">请选择年级</option>' +
                    grades.map(grade => `<option value="${grade.grade}">${grade.grade}年级</option>`).join('');
            } else {
                gradeSelect.innerHTML = '<option value="">该学校暂无年级配置</option>';
            }
        } else {
            gradeSelect.innerHTML = '<option value="">加载年级失败</option>';
        }
    } catch (error) {
        console.error('加载学校年级失败:', error);
        gradeSelect.innerHTML = '<option value="">加载年级失败</option>';
    }
}

// 加载年级的班级配置
async function loadGradeClasses() {
    const schoolSelect = document.getElementById('studentSchool');
    const gradeSelect = document.getElementById('studentGrade');
    const classSelect = document.getElementById('studentClass');

    if (!schoolSelect || !gradeSelect || !classSelect) return;

    const schoolId = schoolSelect.value;
    const grade = gradeSelect.value;

    // 清空班级选择
    classSelect.innerHTML = '<option value="">请选择班级</option>';

    if (!schoolId || !grade) {
        classSelect.innerHTML = '<option value="">请先选择学校和年级</option>';
        return;
    }

    try {
        // 显示加载状态
        classSelect.innerHTML = '<option value="">正在加载班级...</option>';

        // 获取学校的年级配置
        const response = await authFetch(`/api/teacher/schools/${schoolId}/grades`, {}, 2000);

        if (response.ok) {
            const result = await response.json();
            const grades = (result && (result.success === undefined || result.success === true))
                ? (Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []))
                : [];

            // 找到对应年级的配置
            const gradeConfig = grades.find(g => g.grade == grade);

            if (gradeConfig && gradeConfig.class_count > 0) {
                const classOptions = Array.from({length: gradeConfig.class_count}, (_, i) => i + 1)
                    .map(classNum => `<option value="${classNum}">${classNum}班</option>`)
                    .join('');
                classSelect.innerHTML = '<option value="">请选择班级</option>' + classOptions;
            } else {
                classSelect.innerHTML = '<option value="">该年级暂无班级配置</option>';
            }
        } else {
            classSelect.innerHTML = '<option value="">加载班级失败</option>';
        }
    } catch (error) {
        console.error('加载年级班级失败:', error);
        classSelect.innerHTML = '<option value="">加载班级失败</option>';
    }
}

// 导出函数到全局
window.loadSchoolGrades = loadSchoolGrades;
window.loadGradeClasses = loadGradeClasses;

// 重试加载年级配置
async function retryLoadGrades(schoolId) {
    const school = teacherSchools.find(s => s.id == schoolId);
    if (!school) return;

    const container = document.getElementById(`grades-container-${schoolId}`);
    if (!container) return;

    // 清除缓存
    const cacheKey = `grades_${schoolId}`;
    clearCache(cacheKey);

    // 显示重试状态
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="text-muted mt-2 mb-0">正在重试加载年级配置...</p>
        </div>
    `;

    try {
        console.log(`重试加载年级配置: ${school.name}`);
        const resp = await authFetch(`/api/teacher/schools/${schoolId}/grades`, {}, 15000);
        let cfgs = [];

        if (resp.ok) {
            try {
                const result = await resp.json();
                cfgs = (result && (result.success === undefined || result.success === true))
                    ? (Array.isArray(result.data) ? result.data : (Array.isArray(result) ? result : []))
                    : [];
            } catch (parseError) {
                console.warn(`解析年级数据失败: ${school.name}`, parseError);
                cfgs = [];
            }
        }

        // 缓存数据
        setCache(cacheKey, cfgs);

        // 重新渲染
        const html = renderTeacherGradeClassSelection(schoolId, cfgs);
        container.innerHTML = html;

        // 预选任教班级
        precheckTeacherClassesForSchool(schoolId).catch(err => {
            console.warn(`预选任教班级失败: ${school.name}`, err);
        });

        showMessage(`${school.name} 年级配置重新加载成功`, 'success');

    } catch (error) {
        console.error('重试加载年级配置失败:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-times-circle"></i>
                <strong>${school.name}</strong> 年级配置重试失败
                <br><small>错误: ${error.message}</small>
                <br>
                <button class="btn btn-sm btn-primary mt-2" onclick="showAddGradeModal(${schoolId})">
                    <i class="fas fa-plus"></i> 手动添加年级
                </button>
            </div>
        `;
        showMessage(`${school.name} 年级配置重试失败: ${error.message}`, 'error');
    }
}

window.retryLoadGrades = retryLoadGrades;

