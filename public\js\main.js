/**
 * 主模块
 * 负责初始化应用、处理用户登录和模块导航
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化应用
    initApp();
    
    // 确保初始时登录框居中显示
    document.querySelector('.login-box').style.transform = 'translateY(-5vh)';
    document.querySelector('.selector-box').style.transform = 'translateY(-5vh)';
});

/**
 * 初始化应用
 */
function initApp() {
    // 初始化数据库
    DB.init();
    
    // 绑定登录事件
    document.getElementById('login-btn').addEventListener('click', handleLogin);

    // 绑定注册相关事件
    const teacherRegisterLink = document.getElementById('teacher-register-link');
    const showLoginBtn = document.getElementById('show-login');
    const registerBtn = document.getElementById('register-btn');

    if (teacherRegisterLink) {
        teacherRegisterLink.addEventListener('click', showRegisterForm);
    }
    if (showLoginBtn) {
        showLoginBtn.addEventListener('click', showLoginForm);
    }
    if (registerBtn) {
        registerBtn.addEventListener('click', handleRegister);
    }

    // 绑定角色切换事件
    const studentRoleBtn = document.getElementById('student-role-btn');
    if (studentRoleBtn) {
        studentRoleBtn.addEventListener('click', function() {
            window.location.href = '/studentsign';
        });
    }

    // 绑定导航事件（使用安全的事件绑定）
    const medalBtn = document.getElementById('medal-btn');
    const typingBtn = document.getElementById('typing-btn');
    const managementBtn = document.getElementById('management-btn');
    const backBtn = document.getElementById('back-btn');

    if (medalBtn) medalBtn.addEventListener('click', () => loadModule('medal'));
    if (typingBtn) typingBtn.addEventListener('click', () => loadModule('typing'));
    if (managementBtn) managementBtn.addEventListener('click', handleManagement);
    if (backBtn) backBtn.addEventListener('click', backToSelector);
    
    // 绑定退出登录事件
    const logoutBtn = document.getElementById('logout-btn');
    const logoutBtn2 = document.getElementById('logout-btn2');
    const resetDataBtn = document.getElementById('reset-data');

    if (logoutBtn) {
        logoutBtn.addEventListener('click', logout);
    }
    if (logoutBtn2) {
        logoutBtn2.addEventListener('click', logout);
    }

    // 绑定重置数据事件（如果存在）
    if (resetDataBtn) {
        resetDataBtn.addEventListener('click', resetData);
    }
    
    // 检查URL路由
    checkUrlRoute();

    // 监听浏览器后退/前进按钮
    window.addEventListener('popstate', handlePopState);

    // 检查登录状态
    checkLoginStatus();
}

/**
 * 检查用户登录状态
 */
function checkLoginStatus() {
    const user = Utils.getData(CONFIG.STORAGE.USER, null);
    const token = localStorage.getItem('token');

    if (user && token) {
        // 已登录，显示模块选择界面
        document.getElementById('login-container').style.display = 'none';
        document.getElementById('module-selector').style.display = 'flex';
        document.getElementById('module-container').style.display = 'none';
        document.getElementById('user-name').textContent = user.username;

        // 绑定模块选择界面的事件
        setTimeout(() => {
            bindModuleSelectorEvents();
        }, 100);

        // 确保选择器框居中
        document.querySelector('.selector-box').style.transform = 'translateY(-5vh)';

        // 检查是否需要自动加载特定模块
        autoLoadModuleIfNeeded();
    } else {
        // 未登录或token缺失，清除所有登录信息并显示登录界面
        if (user && !token) {
            Utils.removeData(CONFIG.STORAGE.USER);
            localStorage.removeItem('user');
        }

        document.getElementById('login-container').style.display = 'flex';
        document.getElementById('module-selector').style.display = 'none';
        document.getElementById('module-container').style.display = 'none';
    }
}

/**
 * 处理用户登录
 */
function handleLogin() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value.trim();
    
    if (!username || !password) {
        Utils.showMessage('请输入用户名和密码');
        return;
    }
    
    // 显示加载状态
    Utils.showLoading(true);
    
    // 使用数据库API进行登录验证
    DB.login(username, password)
        .then(response => {
            // 提取用户信息
            const userData = response.user;
            
            // 保存token和用户信息
            userData.token = response.token;
            
            // 登录成功，保存用户信息到本地
            Utils.saveData(CONFIG.STORAGE.USER, userData);

            // 同时保存到localStorage（新界面需要）
            localStorage.setItem('token', response.token);
            localStorage.setItem('user', JSON.stringify(userData));

            // 根据用户角色决定跳转
            if (userData.role === 'admin') {
                // 管理员直接跳转到管理界面
                Utils.showMessage(`欢迎回来，${userData.display_name || userData.username}管理员！正在跳转...`);
                setTimeout(() => {
                    window.location.href = '/gl';
                }, 1500);
            } else {
                // 教师显示功能选择界面
                document.getElementById('login-container').style.display = 'none';
                document.getElementById('module-selector').style.display = 'flex';
                document.getElementById('user-name').textContent = userData.display_name || userData.username;

                // 更新URL为/teacher
                window.history.pushState({ module: 'teacher' }, '', '/teacher');

                // 绑定模块选择界面的事件
                setTimeout(() => {
                    bindModuleSelectorEvents();
                }, 100);

                // 显示欢迎信息
                Utils.showMessage(`欢迎回来，${userData.display_name || userData.username}老师`);

                // 检查是否需要自动加载特定模块
                setTimeout(() => {
                    autoLoadModuleIfNeeded();
                }, 200);
            }

            // 隐藏加载状态
            Utils.showLoading(false);
        })
        .catch(error => {
            console.error('登录失败:', error);
            Utils.showMessage('登录失败，请检查用户名和密码');
            Utils.showLoading(false);
        });
}

/**
 * 绑定模块选择界面的事件
 */
function bindModuleSelectorEvents() {
    const managementBtn = document.getElementById('management-btn');
    if (managementBtn) {
        // 移除旧的事件监听器（如果存在）
        managementBtn.removeEventListener('click', handleManagement);
        // 添加新的事件监听器
        managementBtn.addEventListener('click', handleManagement);
        console.log('管理按钮事件已绑定');
    } else {
        console.warn('管理按钮未找到');
    }
}

/**
 * 处理管理按钮点击
 */
function handleManagement() {
    console.log('管理按钮被点击');

    const userData = Utils.getData(CONFIG.STORAGE.USER);
    console.log('用户数据:', userData);

    if (!userData) {
        Utils.showMessage('请先登录');
        return;
    }

    // 根据用户角色跳转到不同的管理界面
    if (userData.role === 'admin') {
        console.log('管理员跳转到 /gl');
        window.location.href = '/gl';
    } else if (userData.role === 'teacher') {
        console.log('教师跳转到 /teacher/gl');
        window.location.href = '/teacher/gl';
    } else {
        console.log('用户角色不支持管理功能:', userData.role);
        Utils.showMessage('您没有管理权限');
    }
}

/**
 * 显示注册表单
 */
function showRegisterForm(e) {
    if (e) {
        e.preventDefault();
    }

    // 隐藏登录容器
    const loginContainer = document.getElementById('login-container');
    const registerContainer = document.getElementById('register-container');

    loginContainer.style.display = 'none';
    registerContainer.style.display = 'flex';

    // 清空注册表单和错误信息
    clearRegisterForm();
    clearFormErrors();

    console.log('切换到注册页面');
}

/**
 * 显示登录表单
 */
function showLoginForm(e) {
    e.preventDefault();

    // 隐藏注册容器
    const loginContainer = document.getElementById('login-container');
    const registerContainer = document.getElementById('register-container');

    registerContainer.style.display = 'none';
    loginContainer.style.display = 'flex';

    // 清空登录表单和错误信息
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
    clearFormErrors();

    console.log('切换到登录页面');
}

/**
 * 处理用户注册
 */
function handleRegister() {
    const username = document.getElementById('reg-username').value.trim();
    const password = document.getElementById('reg-password').value;
    const confirmPassword = document.getElementById('reg-confirm-password').value;

    // 清除之前的错误信息
    clearFormErrors();

    // 客户端验证
    const validationErrors = validateRegistrationForm({
        username, password, confirmPassword
    });

    if (validationErrors.length > 0) {
        showFormErrors(validationErrors);
        return;
    }

    // 显示加载状态
    Utils.showLoading(true);
    const registerBtn = document.getElementById('register-btn');
    registerBtn.disabled = true;
    registerBtn.textContent = '注册中...';

    // 构建注册数据（简化版，只包含用户名和密码）
    const userData = {
        username,
        password,
        confirmPassword
    };

    // 调用注册API
    DB.register(userData)
        .then(response => {
            console.log('注册API响应:', response);

            // 检查响应格式
            let successMessage = '注册成功！请使用新账户登录';
            if (response && response.message) {
                successMessage = response.message;
            }

            // 注册成功
            showFormSuccess(successMessage);

            // 清空表单
            clearRegisterForm();

            // 3秒后自动跳转到登录页面
            setTimeout(() => {
                showLoginForm({ preventDefault: () => {} });
                // 预填用户名
                document.getElementById('username').value = username;
                Utils.showMessage(successMessage);
            }, 2000);
        })
        .catch(error => {
            console.error('=== 注册失败详细调试信息 ===');
            console.error('错误类型:', typeof error);
            console.error('错误对象:', error);
            console.error('错误构造函数:', error?.constructor?.name);
            console.error('错误原型:', Object.getPrototypeOf(error));

            // 尝试获取所有可能的错误信息
            let errorMessage = '注册失败，请稍后重试';
            let debugInfo = [];

            // 详细的错误类型检查
            if (typeof error === 'string') {
                errorMessage = error;
                debugInfo.push('错误类型: 字符串');
            } else if (error instanceof Error) {
                errorMessage = error.message;
                debugInfo.push('错误类型: Error对象');
            } else if (error && typeof error === 'object') {
                debugInfo.push('错误类型: 普通对象');

                // 尝试多种方式提取错误信息
                if (error.message) {
                    errorMessage = error.message;
                    debugInfo.push('使用error.message');
                } else if (error.error) {
                    errorMessage = error.error;
                    debugInfo.push('使用error.error');
                } else if (error.data && error.data.error) {
                    errorMessage = error.data.error;
                    debugInfo.push('使用error.data.error');
                } else {
                    // 列出对象的所有属性
                    const keys = Object.keys(error);
                    debugInfo.push('对象属性: ' + keys.join(', '));

                    if (keys.length > 0) {
                        errorMessage = `错误信息: ${JSON.stringify(error, null, 2)}`;
                    }
                }
            }

            console.error('调试信息:', debugInfo);
            console.error('最终错误消息:', errorMessage);
            console.error('=== 调试信息结束 ===');

            showFormErrors([errorMessage]);
        })
        .finally(() => {
            // 确保加载状态被清除
            Utils.showLoading(false);
            registerBtn.disabled = false;
            registerBtn.textContent = '注册';
        });
}

/**
 * 验证注册表单
 */
function validateRegistrationForm({ username, password, confirmPassword }) {
    const errors = [];

    // 用户名验证
    if (!username) {
        errors.push('用户名不能为空');
    } else if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
        errors.push('用户名必须是3-20个字符，只能包含字母、数字和下划线');
    }

    // 密码验证
    if (!password) {
        errors.push('密码不能为空');
    } else if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/.test(password)) {
        errors.push('密码必须至少8位，包含字母和数字');
    }

    // 确认密码验证
    if (!confirmPassword) {
        errors.push('请确认密码');
    } else if (password !== confirmPassword) {
        errors.push('两次输入的密码不一致');
    }

    return errors;
}

/**
 * 清空注册表单
 */
function clearRegisterForm() {
    document.getElementById('reg-username').value = '';
    document.getElementById('reg-password').value = '';
    document.getElementById('reg-confirm-password').value = '';
}

/**
 * 显示表单错误信息
 */
function showFormErrors(errors) {
    console.log('=== showFormErrors 调试 ===');
    console.log('接收到的errors:', errors);
    console.log('errors类型:', typeof errors);
    console.log('是否为数组:', Array.isArray(errors));

    clearFormErrors();

    // 确保errors是数组
    const errorArray = Array.isArray(errors) ? errors : [errors];
    console.log('转换后的errorArray:', errorArray);

    const errorDiv = document.createElement('div');
    errorDiv.className = 'form-error';

    // 处理每个错误项
    const processedErrors = errorArray.map((error, index) => {
        console.log(`处理错误 ${index}:`, error, '类型:', typeof error);

        if (typeof error === 'string' && error.trim()) {
            return error.trim();
        } else if (error && typeof error === 'object') {
            console.warn('检测到对象错误:', error);

            // 尝试从对象中提取有意义的信息
            if (error.message) {
                return error.message;
            } else if (error.error) {
                return error.error;
            } else {
                // 如果是纯对象，转换为可读字符串
                try {
                    return `错误详情: ${JSON.stringify(error)}`;
                } catch (e) {
                    return '发生了未知错误';
                }
            }
        } else if (error !== null && error !== undefined) {
            // 其他类型，转换为字符串
            return String(error);
        }
        return null;
    }).filter(error => error !== null);

    console.log('处理后的错误:', processedErrors);

    if (processedErrors.length > 0) {
        errorDiv.innerHTML = processedErrors.map(error => `• ${error}`).join('<br>');

        const registerForm = document.querySelector('.register-form');
        if (registerForm) {
            registerForm.insertBefore(errorDiv, registerForm.firstChild);
        } else {
            console.error('找不到.register-form元素');
        }
    } else {
        console.warn('没有有效的错误信息可显示');
    }

    console.log('=== showFormErrors 调试结束 ===');
}

/**
 * 显示表单成功信息
 */
function showFormSuccess(message) {
    clearFormErrors();

    const successDiv = document.createElement('div');
    successDiv.className = 'form-success';
    successDiv.textContent = message;

    const registerForm = document.querySelector('.register-form');
    registerForm.insertBefore(successDiv, registerForm.firstChild);
}

/**
 * 清除表单错误和成功信息
 */
function clearFormErrors() {
    const existingErrors = document.querySelectorAll('.form-error, .form-success');
    existingErrors.forEach(el => el.remove());
}

/**
 * 退出登录
 */
function logout() {
    // 清除用户数据
    localStorage.removeItem(CONFIG.STORAGE.USER);
    
    // 更新界面
    document.getElementById('login-container').style.display = 'flex';
    document.getElementById('module-selector').style.display = 'none';
    document.getElementById('module-container').style.display = 'none';
    
    // 确保登录框居中
    document.querySelector('.login-box').style.transform = 'translateY(-5vh)';
    
    // 清空登录表单
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
    
    Utils.showMessage('已退出登录');
}

/**
 * 重置数据
 */
function resetData() {
    Utils.confirm('警告：这将清除所有数据，确定要继续吗？').then(confirmed => {
        if (confirmed) {
            Utils.showLoading(true);
            
            // 显示正在清除数据的消息
            Utils.showMessage('正在清除数据...');
            
            // 使用数据库API清除数据（这里假设要清除所有奖章和打字记录）
            // 首先获取所有学生，然后重置他们的奖章数量
            DB.findStudent()
                .then(students => {
                    if (!students || students.length === 0) {
                        return Promise.resolve();
                    }
                    
                    // 创建每个学生重置奖章的Promise数组
                    const resetPromises = students.map(student => {
                        return DB.updateMedals(student.student_identifier, 0);
                    });
                    
                    // 执行所有Promise
                    return Promise.all(resetPromises);
                })
                .then(() => {
                    // 记录重置操作
                    return DB.logAction('reset_data', {
                        user_id: Utils.getData(CONFIG.STORAGE.USER).id,
                        timestamp: new Date().toISOString()
                    });
                })
                .then(() => {
                    Utils.showMessage('数据已重置');
                    Utils.showLoading(false);
                    
                    // 刷新模块数据
                    if (MedalModule && MedalModule.initialized) {
                        MedalModule.loadData();
                    }
                    if (TypingModule && TypingModule.initialized) {
                        TypingModule.loadData();
                    }
                })
                .catch(error => {
                    console.error('重置数据失败:', error);
                    Utils.showMessage('重置数据失败，请重试');
                    Utils.showLoading(false);
                });
        }
    });
}

/**
 * 加载模块
 * @param {string} moduleName - 模块名称
 */
function loadModule(moduleName) {
    // 显示加载状态
    Utils.showLoading(true);

    // 更新模块标题
    const moduleTitle = document.getElementById('module-title');
    moduleTitle.textContent = moduleName === 'medal' ? '奖章管理' : '打字管理';

    // 更新URL但不刷新页面
    let newUrl = '/teacher';
    if (moduleName === 'medal') {
        newUrl = '/teacher/medal';
    } else if (moduleName === 'typing') {
        newUrl = '/teacher/type';
    } else if (moduleName === 'teacher-management') {
        newUrl = '/teacher/gl';
    } else if (moduleName === 'admin') {
        newUrl = '/gl';
    }

    if (window.location.pathname !== newUrl) {
        window.history.pushState({ module: moduleName }, '', newUrl);
    }

    // 隐藏所有模块
    document.querySelectorAll('.module-content').forEach(module => {
        module.style.display = 'none';
    });

    // 显示模块容器
    document.getElementById('module-container').style.display = 'block';
    document.getElementById('module-selector').style.display = 'none';

    // 确保内容区域占据整个屏幕
    document.body.style.height = '100%';
    document.body.style.overflow = 'auto';
    
    // 根据模块名称加载相应的模块
    if (moduleName === 'medal') {
        // 显示奖章模块
        document.getElementById('medal-module').style.display = 'block';
        
        // 延迟加载模块，确保界面更新后再加载数据
        setTimeout(() => {
            // 如果还没初始化过，则先初始化
            if (!MedalModule.initialized) {
                MedalModule.init();
                MedalModule.initialized = true;
            } else {
                // 如果已初始化，则刷新数据
                MedalModule.loadData();
            }
            Utils.showLoading(false);
        }, 300);
    } else {
        // 显示打字模块
        document.getElementById('typing-module').style.display = 'block';
        
        // 延迟加载模块，确保界面更新后再加载数据
        setTimeout(() => {
            // 如果还没初始化过，则先初始化
            if (!TypingModule.initialized) {
                TypingModule.init();
                TypingModule.initialized = true;
            } else {
                // 如果已初始化，则刷新数据
                TypingModule.loadData();
            }
            Utils.showLoading(false);
        }, 300);
    }
}

/**
 * 返回模块选择界面
 */
function backToSelector() {
    document.getElementById('module-container').style.display = 'none';
    document.getElementById('module-selector').style.display = 'flex';
    
    // 恢复body的布局样式
    document.body.style.height = '100vh';
    document.body.style.overflow = 'hidden';
    
    // 确保选择器框居中
    document.querySelector('.selector-box').style.transform = 'translateY(-5vh)';
}

/**
 * 检查URL路由，支持直接访问特定功能
 */
function checkUrlRoute() {
    const path = window.location.pathname;

    // 教师注册路径
    if (path === '/reg') {
        // 直接显示注册界面
        showRegisterForm();
        return;
    }

    // 教师相关路径
    if (path === '/teacher') {
        sessionStorage.setItem('autoLoadModule', 'teacher');
    }
    else if (path === '/teacher/medal') {
        sessionStorage.setItem('autoLoadModule', 'medal');
    }
    else if (path === '/teacher/type') {
        sessionStorage.setItem('autoLoadModule', 'typing');
    }
    else if (path === '/teacher/gl') {
        sessionStorage.setItem('autoLoadModule', 'teacher-management');
    }
    // 系统管理员路径
    else if (path === '/gl') {
        sessionStorage.setItem('autoLoadModule', 'admin');
    }
}

/**
 * 自动加载指定模块（在登录成功后调用）
 */
function autoLoadModuleIfNeeded() {
    const autoLoadModule = sessionStorage.getItem('autoLoadModule');
    if (autoLoadModule) {
        sessionStorage.removeItem('autoLoadModule');
        // 延迟一点时间确保界面已经准备好
        setTimeout(() => {
            loadModule(autoLoadModule);
        }, 100);
    }
}

/**
 * 处理浏览器后退/前进按钮
 */
function handlePopState(event) {
    const path = window.location.pathname;
    const user = Utils.getData(CONFIG.STORAGE.USER, null);

    if (!user) {
        // 未登录，显示登录界面
        document.getElementById('login-container').style.display = 'flex';
        document.getElementById('module-selector').style.display = 'none';
        document.getElementById('module-container').style.display = 'none';
        return;
    }

    if (path === '/teacher/medal') {
        // 进入奖章管理
        loadModule('medal');
    } else if (path === '/teacher/type') {
        // 进入打字管理
        loadModule('typing');
    } else if (path === '/teacher/gl') {
        // 进入教师管理
        loadModule('teacher-management');
    } else if (path === '/gl') {
        // 进入系统管理员
        loadModule('admin');
    } else if (path === '/teacher' || path === '/') {
        // 返回模块选择界面
        document.getElementById('module-container').style.display = 'none';
        document.getElementById('module-selector').style.display = 'flex';
        document.getElementById('login-container').style.display = 'none';

        // 确保URL显示为/teacher
        if (path === '/' && window.location.pathname === '/') {
            window.history.pushState({ module: 'teacher' }, '', '/teacher');
        }
    }
}