/**
 * 教师专用路由
 * 实现分级管理和权限控制
 */

const express = require('express');
const router = express.Router();
const teacherController = require('../controllers/teacherController');

// JWT认证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ error: '访问令牌是必需的' });
  }

  const jwt = require('jsonwebtoken');
  jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key-change-in-production', (err, user) => {
    if (err) {
      console.error('Token验证失败:', err);
      return res.status(403).json({ error: '无效的访问令牌' });
    }

    req.user = user;
    next();
  });
};

// ==================== 分级管理路由 ====================

// 获取教师关联的学校
router.get('/schools', authenticateToken, teacherController.getTeacherSchools);

// 获取教师可管理的学生
router.get('/students', authenticateToken, teacherController.getTeacherStudents);

// 获取教师可管理的班级
router.get('/classes', authenticateToken, teacherController.getTeacherClasses);

// 添加学校
router.post('/schools', authenticateToken, teacherController.addTeacherSchool);

// 更新学校信息
router.put('/schools/:id', authenticateToken, teacherController.updateTeacherSchool);

// 删除学校
router.delete('/schools/:id', authenticateToken, teacherController.deleteTeacherSchool);

// 添加年级（到指定学校）
router.post('/grades', authenticateToken, teacherController.addGrade);

// 添加班级权限（给教师分配特定班级的权限）
router.post('/class-permissions', authenticateToken, teacherController.addClassPermission);

// ==================== 学生管理路由（带权限检查）====================

// 添加学生（带权限检查）
router.post('/students', authenticateToken, async (req, res) => {
  try {
    const { name, student_id, school_id, grade, class: studentClass } = req.body;
    if (!name || !name.trim() || !grade || !studentClass) {
      return res.status(400).json({
        success: false,
        message: '学生姓名、年级和班级为必填项'
      });
    }

    const db = require('../config/db');

    // 检查权限 - 教师必须有对应班级的权限
    if (req.user.role !== 'admin' && school_id && grade && studentClass) {
      const { data: permission } = await db.supabase
        .from('teacher_class_permissions')
        .select('*')
        .eq('teacher_id', req.user.id)
        .eq('school_id', school_id)
        .eq('grade', grade)
        .eq('class', studentClass)
        .single();

      if (!permission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限在此班级添加学生'
        });
      }
    }

    // 生成学生标识符（如果未提供）
    const studentIdentifier = student_id || `STU_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 检查学生标识符是否已存在
    const { data: existingStudent } = await db.supabase
      .from('students')
      .select('student_identifier')
      .eq('student_identifier', studentIdentifier)
      .single();

    if (existingStudent) {
      return res.status(400).json({
        success: false,
        message: '学生标识符已存在'
      });
    }

    // 插入学生
    const { data, error } = await db.supabase
      .from('students')
      .insert({
        student_identifier: studentIdentifier,
        name: name.trim(),
        grade: parseInt(grade),
        class: parseInt(studentClass),
        school_id: school_id ? parseInt(school_id) : null,
        created_at: new Date().toISOString()
      })
      .select(`
        *,
        schools(id, name)
      `)
      .single();

    if (error) {
      console.error('添加学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '添加学生失败',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: '学生添加成功',
      data
    });

  } catch (error) {
    console.error('添加学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 更新学生信息（带权限检查）
router.put('/students/:id', authenticateToken, async (req, res) => {
  try {
    const studentId = req.params.id;
    const { name, student_id, grade, class: studentClass } = req.body;
    const db = require('../config/db');

    // 获取现有学生信息
    const { data: existingStudent, error: fetchError } = await db.supabase
      .from('students')
      .select('*')
      .eq('id', studentId)
      .single();

    if (fetchError || !existingStudent) {
      return res.status(404).json({
        success: false,
        message: '学生不存在'
      });
    }

    // 检查权限
    if (req.user.role !== 'admin') {
      const { data: permission } = await db.supabase
        .from('teacher_class_permissions')
        .select('*')
        .eq('teacher_id', req.user.id)
        .eq('school_id', existingStudent.school_id)
        .eq('grade', existingStudent.grade)
        .eq('class', existingStudent.class)
        .single();

      if (!permission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限修改此学生信息'
        });
      }
    }

    // 构建更新数据
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (name) updateData.name = name.trim();
    if (student_id !== undefined) updateData.student_identifier = student_id.trim() || existingStudent.student_identifier;
    if (grade) updateData.grade = parseInt(grade);
    if (studentClass) updateData.class = parseInt(studentClass);

    const { data, error } = await db.supabase
      .from('students')
      .update(updateData)
      .eq('id', studentId)
      .select(`
        *,
        schools(id, name)
      `)
      .single();

    if (error) {
      console.error('更新学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '更新学生失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: '学生信息更新成功',
      data
    });

  } catch (error) {
    console.error('更新学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 删除学生（带权限检查）
router.delete('/students/:id', authenticateToken, async (req, res) => {
  try {
    const studentId = req.params.id;
    const db = require('../config/db');

    // 获取现有学生信息
    const { data: existingStudent, error: fetchError } = await db.supabase
      .from('students')
      .select('*')
      .eq('id', studentId)
      .single();

    if (fetchError || !existingStudent) {
      return res.status(404).json({
        success: false,
        message: '学生不存在'
      });
    }

    // 检查权限
    if (req.user.role !== 'admin') {
      const { data: permission } = await db.supabase
        .from('teacher_class_permissions')
        .select('*')
        .eq('teacher_id', req.user.id)
        .eq('school_id', existingStudent.school_id)
        .eq('grade', existingStudent.grade)
        .eq('class', existingStudent.class)
        .single();

      if (!permission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限删除此学生'
        });
      }
    }

    const { data, error } = await db.supabase
      .from('students')
      .delete()
      .eq('id', studentId)
      .select()
      .single();

    if (error) {
      console.error('删除学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '删除学生失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: '学生删除成功'
    });

  } catch (error) {
    console.error('删除学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// ==================== Excel导入功能 ====================

// Excel模板下载
router.get('/excel/template', authenticateToken, async (req, res) => {
  try {
    const { school_id, grade, class: className } = req.query;
    
    // 检查参数
    if (!school_id || !grade || !className) {
      return res.status(400).json({
        success: false,
        message: '请提供学校ID、年级和班级参数'
      });
    }

    // 检查权限
    if (req.user.role !== 'admin') {
      const { data: permission } = await db.supabase
        .from('teacher_class_permissions')
        .select('*')
        .eq('teacher_id', req.user.id)
        .eq('school_id', school_id)
        .eq('grade', grade)
        .eq('class', className)
        .single();

      if (!permission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限访问此班级'
        });
      }
    }

    // 生成Excel模板
    const templateData = {
      filename: `学生导入模板_${grade}年级${className}班.xlsx`,
      columns: [
        { header: '学号', key: 'student_id', width: 15 },
        { header: '姓名', key: 'name', width: 10 },
        { header: '性别', key: 'gender', width: 8 },
        { header: '座位号', key: 'seat_number', width: 10 }
      ],
      sampleData: [
        { student_id: '2024001', name: '张三', gender: '男', seat_number: 1 },
        { student_id: '2024002', name: '李四', gender: '女', seat_number: 2 }
      ]
    };

    res.json({
      success: true,
      message: '模板数据生成成功',
      data: templateData
    });

  } catch (error) {
    console.error('生成Excel模板失败:', error);
    res.status(500).json({
      success: false,
      message: '生成模板失败',
      error: error.message
    });
  }
});

// Excel学生数据导入
router.post('/excel/import', authenticateToken, async (req, res) => {
  try {
    const { school_id, grade, class: className, students } = req.body;
    
    if (!school_id || !grade || !className || !students || !Array.isArray(students)) {
      return res.status(400).json({
        success: false,
        message: '请提供完整的导入参数'
      });
    }

    const db = require('../config/db');

    // 检查权限
    if (req.user.role !== 'admin') {
      const { data: permission } = await db.supabase
        .from('teacher_class_permissions')
        .select('*')
        .eq('teacher_id', req.user.id)
        .eq('school_id', school_id)
        .eq('grade', grade)
        .eq('class', className)
        .single();

      if (!permission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限在此班级导入学生'
        });
      }
    }

    // 批量处理学生数据
    const results = [];
    const errors = [];

    for (let i = 0; i < students.length; i++) {
      const student = students[i];
      
      try {
        // 验证必填字段
        if (!student.name || !student.name.trim()) {
          errors.push(`第${i + 1}行：学生姓名不能为空`);
          continue;
        }

        const studentIdentifier = student.student_id || `STU_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // 检查学号是否重复
        const { data: existing } = await db.supabase
          .from('students')
          .select('student_identifier')
          .eq('student_identifier', studentIdentifier)
          .single();

        if (existing) {
          errors.push(`第${i + 1}行：学号${studentIdentifier}已存在`);
          continue;
        }

        // 插入学生
        const { data, error } = await db.supabase
          .from('students')
          .insert({
            student_identifier: studentIdentifier,
            name: student.name.trim(),
            grade: parseInt(grade),
            class: parseInt(className),
            school_id: parseInt(school_id),
            gender: student.gender || null,
            seat_number: student.seat_number ? parseInt(student.seat_number) : null,
            created_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) {
          errors.push(`第${i + 1}行：${error.message}`);
        } else {
          results.push(data);
        }

      } catch (error) {
        errors.push(`第${i + 1}行：处理失败 - ${error.message}`);
      }
    }

    res.json({
      success: true,
      message: `导入完成，成功${results.length}条，失败${errors.length}条`,
      data: {
        successful: results.length,
        failed: errors.length,
        errors: errors
      }
    });

  } catch (error) {
    console.error('Excel导入失败:', error);
    res.status(500).json({
      success: false,
      message: '导入失败',
      error: error.message
    });
  }
});

// 批量删除学生
router.post('/students/batch-delete', authenticateToken, async (req, res) => {
  try {
    console.log('批量删除请求体:', req.body);
    const { student_ids, studentIds } = req.body;
    const rawIds = student_ids || studentIds; // 兼容两种参数名
    console.log('原始学生ID:', rawIds);

    // 确保ID是整数数组
    const ids = rawIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id));
    console.log('转换后的学生ID:', ids);

    if (!rawIds || !Array.isArray(rawIds) || rawIds.length === 0) {
      console.log('学生ID列表为空或无效');
      return res.status(400).json({
        success: false,
        message: '请提供要删除的学生ID列表'
      });
    }

    if (ids.length === 0) {
      console.log('没有有效的学生ID');
      return res.status(400).json({
        success: false,
        message: '没有有效的学生ID'
      });
    }

    const db = require('../config/db');

    // 检查权限 - 验证用户是否有权限删除这些学生
    if (req.user.role !== 'admin') {
      // 获取要删除的学生信息
      const { data: students, error: fetchError } = await db.supabase
        .from('students')
        .select('id, school_id, grade, class')
        .in('id', ids);

      if (fetchError) {
        console.error('获取学生信息失败:', fetchError);
        return res.status(500).json({
          success: false,
          message: '获取学生信息失败',
          error: fetchError.message
        });
      }

      // 检查每个学生的权限
      for (const student of students) {
        const { data: permission } = await db.supabase
          .from('teacher_class_permissions')
          .select('*')
          .eq('teacher_id', req.user.id)
          .eq('school_id', student.school_id)
          .eq('grade', student.grade)
          .eq('class', student.class)
          .single();

        if (!permission) {
          return res.status(403).json({
            success: false,
            message: `您没有权限删除学生ID ${student.id}`
          });
        }
      }
    }

    // 执行批量删除
    console.log('开始执行数据库删除操作...');
    const { data, error } = await db.supabase
      .from('students')
      .delete()
      .in('id', ids)
      .select();

    console.log('数据库删除操作结果:', { data, error });

    if (error) {
      console.error('批量删除学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '批量删除学生失败',
        error: error.message
      });
    }

    console.log(`成功删除 ${data ? data.length : 0} 个学生`);
    res.json({
      success: true,
      message: `成功删除 ${data.length} 个学生`,
      deleted_count: data.length
    });

  } catch (error) {
    console.error('批量删除学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 批量更新学生信息
router.post('/students/batch-update', authenticateToken, async (req, res) => {
  try {
    const { student_ids, studentIds, update_data, updates } = req.body;
    const ids = student_ids || studentIds; // 兼容两种参数名
    const updateData = update_data || updates; // 兼容两种参数名

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要更新的学生ID列表'
      });
    }

    if (!updateData || typeof updateData !== 'object') {
      return res.status(400).json({
        success: false,
        message: '请提供更新数据'
      });
    }

    const db = require('../config/db');

    // 检查权限
    if (req.user.role !== 'admin') {
      const { data: students, error: fetchError } = await db.supabase
        .from('students')
        .select('id, school_id, grade, class')
        .in('id', ids);

      if (fetchError) {
        console.error('获取学生信息失败:', fetchError);
        return res.status(500).json({
          success: false,
          message: '获取学生信息失败',
          error: fetchError.message
        });
      }

      for (const student of students) {
        const { data: permission } = await db.supabase
          .from('teacher_class_permissions')
          .select('*')
          .eq('teacher_id', req.user.id)
          .eq('school_id', student.school_id)
          .eq('grade', student.grade)
          .eq('class', student.class)
          .single();

        if (!permission) {
          return res.status(403).json({
            success: false,
            message: `您没有权限更新学生ID ${student.id}`
          });
        }
      }
    }

    // 准备更新数据
    const finalUpdateData = {
      ...updateData,
      updated_at: new Date().toISOString()
    };

    // 执行批量更新
    const { data, error } = await db.supabase
      .from('students')
      .update(finalUpdateData)
      .in('id', ids)
      .select();

    if (error) {
      console.error('批量更新学生失败:', error);
      return res.status(500).json({
        success: false,
        message: '批量更新学生失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: `成功更新 ${data.length} 个学生`,
      updated_count: data.length
    });

  } catch (error) {
    console.error('批量更新学生API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 批量转班
router.post('/students/batch-transfer', authenticateToken, async (req, res) => {
  try {
    const { student_ids, target_school_id, target_grade, target_class, reason } = req.body;

    if (!student_ids || !Array.isArray(student_ids) || student_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要转班的学生ID列表'
      });
    }

    if (!target_school_id || !target_grade || !target_class) {
      return res.status(400).json({
        success: false,
        message: '请提供目标学校、年级和班级'
      });
    }

    const db = require('../config/db');

    // 检查权限
    if (req.user.role !== 'admin') {
      const { data: students, error: fetchError } = await db.supabase
        .from('students')
        .select('id, school_id, grade, class')
        .in('id', student_ids);

      if (fetchError) {
        console.error('获取学生信息失败:', fetchError);
        return res.status(500).json({
          success: false,
          message: '获取学生信息失败',
          error: fetchError.message
        });
      }

      // 检查原班级权限
      for (const student of students) {
        const { data: permission } = await db.supabase
          .from('teacher_class_permissions')
          .select('*')
          .eq('teacher_id', req.user.id)
          .eq('school_id', student.school_id)
          .eq('grade', student.grade)
          .eq('class', student.class)
          .single();

        if (!permission) {
          return res.status(403).json({
            success: false,
            message: `您没有权限转移学生ID ${student.id}`
          });
        }
      }

      // 检查目标班级权限
      const { data: targetPermission } = await db.supabase
        .from('teacher_class_permissions')
        .select('*')
        .eq('teacher_id', req.user.id)
        .eq('school_id', target_school_id)
        .eq('grade', target_grade)
        .eq('class', target_class)
        .single();

      if (!targetPermission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限将学生转入目标班级'
        });
      }
    }

    // 执行批量转班
    const { data, error } = await db.supabase
      .from('students')
      .update({
        school_id: target_school_id,
        grade: target_grade,
        class: target_class,
        updated_at: new Date().toISOString()
      })
      .in('id', student_ids)
      .select();

    if (error) {
      console.error('批量转班失败:', error);
      return res.status(500).json({
        success: false,
        message: '批量转班失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: `成功转班 ${data.length} 个学生`,
      transferred_count: data.length
    });

  } catch (error) {
    console.error('批量转班API错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

module.exports = router;