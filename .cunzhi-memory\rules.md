# 开发规范和规则

- 管理后台功能优化要求：教师管理增强（任教学校多选、所教年级多选、任课班级动态显示、重置密码、删除功能），学校管理简化（只保留名称字段），学生管理增强（所属学校、任课教师匹配），移除班级管理和权限分配菜单，所有操作使用Bootstrap模态框，实时同步Supabase数据库
- 用户要求修改打字管理功能：1.将URL后缀改为/type 2.将数据库从MySQL迁移到Supabase 3.在筛选条件中添加学校选项 4.实现教师权限控制，只能查看本人所教学生的打字成绩，逻辑与奖章管理一致
- 用户要求删除打字管理中的增加、减少功能，学生打字速度不需要教师手动增减，教师只需要查看数据即可
- 用户要求：1.优化筛选功能用户体验，减少每次点击都显示加载中的问题 2.创建学生端独立HTML页面，URL为/stusign（签到界面）和/stutype（打字练习界面），从原有index.html中分离学生功能
