<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师管理中心 - 班级成绩管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"
          onerror="handleCSSLoadError(this, 'Bootstrap')">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet"
          onerror="handleCSSLoadError(this, 'FontAwesome')">

    <script>
        function handleCSSLoadError(element, name) {
            console.warn(name + ' CSS加载失败，添加基础样式');
            if (name === 'Bootstrap') {
                // 添加基础的Bootstrap样式
                const style = document.createElement('style');
                style.textContent = `
                    .btn { padding: 0.375rem 0.75rem; margin-bottom: 0; font-size: 1rem; border-radius: 0.375rem; border: 1px solid transparent; cursor: pointer; }
                    .btn-primary { color: #fff; background-color: #0d6efd; border-color: #0d6efd; }
                    .btn-success { color: #fff; background-color: #198754; border-color: #198754; }
                    .btn-secondary { color: #fff; background-color: #6c757d; border-color: #6c757d; }
                    .modal { position: fixed; top: 0; left: 0; z-index: 1055; display: none; width: 100%; height: 100%; }
                    .modal.show { display: block !important; }
                    .modal-dialog { position: relative; width: auto; margin: 0.5rem; }
                    .modal-content { position: relative; background-color: #fff; border: 1px solid rgba(0,0,0,.2); border-radius: 0.3rem; }
                    .modal-header { padding: 1rem; border-bottom: 1px solid #dee2e6; }
                    .modal-body { position: relative; flex: 1 1 auto; padding: 1rem; }
                    .modal-footer { padding: 1rem; border-top: 1px solid #dee2e6; }
                    .form-control { display: block; width: 100%; padding: 0.375rem 0.75rem; font-size: 1rem; border: 1px solid #ced4da; border-radius: 0.375rem; }
                    .form-select { display: block; width: 100%; padding: 0.375rem 2.25rem 0.375rem 0.75rem; font-size: 1rem; border: 1px solid #ced4da; border-radius: 0.375rem; }
                    .alert { position: relative; padding: 0.75rem 1.25rem; margin-bottom: 1rem; border: 1px solid transparent; border-radius: 0.375rem; }
                    .alert-info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
                    .alert-success { color: #0f5132; background-color: #d1e7dd; border-color: #badbcc; }
                    .alert-danger { color: #842029; background-color: #f8d7da; border-color: #f5c2c7; }
                    .table { width: 100%; margin-bottom: 1rem; color: #212529; border-collapse: collapse; }
                    .table th, .table td { padding: 0.75rem; vertical-align: top; border-top: 1px solid #dee2e6; }
                    .table thead th { vertical-align: bottom; border-bottom: 2px solid #dee2e6; }
                    .card { position: relative; display: flex; flex-direction: column; background-color: #fff; border: 1px solid rgba(0,0,0,.125); border-radius: 0.375rem; }
                    .card-header { padding: 0.75rem 1.25rem; margin-bottom: 0; background-color: rgba(0,0,0,.03); border-bottom: 1px solid rgba(0,0,0,.125); }
                    .card-body { flex: 1 1 auto; padding: 1.25rem; }
                    .spinner-border { display: inline-block; width: 2rem; height: 2rem; vertical-align: text-bottom; border: 0.25em solid currentColor; border-right-color: transparent; border-radius: 50%; animation: spinner-border .75s linear infinite; }
                    @keyframes spinner-border { to { transform: rotate(360deg); } }
                `;
                document.head.appendChild(style);
            }
        }
    </script>

    <style>
        /* 教师界面样式 - 采用管理员界面相同的设计风格 */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        /* 修复Modal backdrop问题 */
        .modal-backdrop {
            display: none !important; /* 强制隐藏backdrop */
        }
        
        /* 或者调整z-index层级关系 */
        .modal {
            z-index: 1060 !important;
        }
        
        .modal-dialog {
            z-index: 1061 !important;
        }
        
        /* 确保模态框内容可以正常交互 */
        .modal-content {
            position: relative;
            z-index: 1062 !important;
            pointer-events: auto;
        }
        
        /* 防止body滚动锁定问题 */
        body.modal-open {
            overflow: auto !important;
            padding-right: 0 !important;
        }

        .admin-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            margin: 20px;
            min-height: calc(100vh - 40px);
            overflow: hidden;
        }

        .admin-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .admin-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .admin-header h1 i {
            margin-right: 15px;
            font-size: 32px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .admin-body {
            display: flex;
            height: calc(100vh - 120px);
        }

        .admin-sidebar {
            width: 280px;
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            border-right: 1px solid #dee2e6;
            padding: 0;
        }

        .nav-link {
            color: #495057;
            padding: 15px 25px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-left: 4px solid #20c997;
        }

        .nav-link i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .admin-content {
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
            overflow-y: auto;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-2px);
        }

        /* 内容区域样式 */
        .management-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <!-- 教师管理界面主容器 -->
    <div class="admin-container">
        <!-- 顶部导航栏 -->
        <div class="admin-header">
            <h1>
                <i class="fas fa-chalkboard-teacher"></i>
                教师管理中心
            </h1>
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div>
                    <div style="font-weight: 600;" id="user-display-name">加载中...</div>
                    <div style="font-size: 14px; opacity: 0.8;" id="user-role">教师</div>
                </div>
                <a href="#" class="logout-btn" onclick="teacherLogout()">
                    <i class="fas fa-sign-out-alt me-2"></i>退出登录
                </a>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="admin-body">
            <!-- 左侧导航栏 -->
            <div class="admin-sidebar">
                <a href="#" class="nav-link active" onclick="showTeacherSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i> 控制台概览
                </a>
                <a href="#" class="nav-link" onclick="showTeacherSection('school-management')">
                    <i class="fas fa-school"></i> 学校管理
                </a>
                <a href="#" class="nav-link" onclick="showTeacherSection('grade-management')">
                    <i class="fas fa-layer-group"></i> 年级管理
                </a>
                <a href="#" class="nav-link" onclick="showTeacherSection('student-management')">
                    <i class="fas fa-users"></i> 学生管理
                </a>
                <a href="#" class="nav-link" onclick="showTeacherSection('profile')">
                    <i class="fas fa-user-cog"></i> 个人设置
                </a>
            </div>

            <!-- 右侧内容区域 -->
            <div class="admin-content">
                <div id="teacher-content-area">
                    <!-- 内容将通过JavaScript动态加载 -->
                    <div class="text-center py-5">
                        <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
                        <p class="text-muted">正在加载...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script>
        // 预定义错误处理函数
        function handleBootstrapLoadError() {
            console.warn('Bootstrap CDN加载失败，创建基础替代对象');
            window.bootstrap = {
                Modal: function(element, options) {
                    return {
                        element: element,
                        show: function() {
                            if (this.element) {
                                this.element.style.display = 'block';
                                this.element.classList.add('show');
                                // 添加backdrop
                                const backdrop = document.createElement('div');
                                backdrop.className = 'modal-backdrop fade show';
                                backdrop.id = 'modal-backdrop-' + Date.now();
                                document.body.appendChild(backdrop);
                                this.backdrop = backdrop;
                            }
                        },
                        hide: function() {
                            if (this.element) {
                                this.element.style.display = 'none';
                                this.element.classList.remove('show');
                                // 移除backdrop
                                if (this.backdrop) {
                                    this.backdrop.remove();
                                }
                            }
                        }
                    };
                }
            };
            // 添加静态方法
            bootstrap.Modal.getInstance = function(element) {
                return new bootstrap.Modal(element);
            };
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
            onerror="handleBootstrapLoadError()"></script>

    <!-- XLSX库 - 使用本地版本 -->
    <script src="/js/lib/xlsx.full.min.js"
            onerror="handleXLSXLoadError()"></script>

    <!-- 错误处理脚本已在上面定义，这里删除重复 -->

        // XLSX加载失败处理
        function handleXLSXLoadError() {
            console.warn('XLSX库加载失败，Excel功能将不可用');
            window.XLSX = {
                utils: {
                    json_to_sheet: function() {
                        console.error('XLSX库未加载，无法导出Excel');
                        return null;
                    },
                    book_new: function() {
                        console.error('XLSX库未加载，无法创建Excel');
                        return null;
                    },
                    book_append_sheet: function() {
                        console.error('XLSX库未加载，无法添加工作表');
                    }
                },
                writeFile: function() {
                    console.error('XLSX库未加载，无法保存Excel文件');
                    alert('Excel功能暂时不可用，请稍后重试');
                }
            };
        }
    </script>
    
    <!-- 工具函数 -->
    <script src="/js/utils.js"></script>
    <script src="/js/config.js"></script>

    <!-- Supabase实时客户端 -->
    <script src="/js/supabase-client.js"></script>

    <!-- 教师管理JavaScript -->
    <script src="/js/teacher-management.js"></script>

    <!-- 页面加载完成检查 -->
    <script>
        // 页面加载完成后的检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');

            // 检查关键函数是否存在
            const checkFunctions = [
                'initTeacherManagement',
                'showTeacherSection',
                'showMessage'
            ];

            checkFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    console.log(`✓ ${funcName} 函数已加载`);
                } else {
                    console.error(`✗ ${funcName} 函数未找到`);
                }
            });

            // 检查关键库是否加载
            if (typeof bootstrap !== 'undefined') {
                console.log('✓ Bootstrap已加载');
            } else {
                console.warn('⚠ Bootstrap未加载，使用降级模式');
            }

            if (typeof XLSX !== 'undefined') {
                console.log('✓ XLSX已加载');
            } else {
                console.warn('⚠ XLSX未加载，Excel功能不可用');
            }
        });

        // 页面完全加载后启动应用
        window.addEventListener('load', function() {
            console.log('页面完全加载完成，启动应用...');

            // 延迟启动，确保所有资源都已加载
            setTimeout(() => {
                if (typeof initTeacherManagement === 'function') {
                    initTeacherManagement();
                } else {
                    console.error('initTeacherManagement函数未找到，无法启动应用');
                    // 显示错误信息给用户
                    const contentArea = document.getElementById('teacher-content-area');
                    if (contentArea) {
                        contentArea.innerHTML = `
                            <div class="alert alert-danger text-center">
                                <h4>应用启动失败</h4>
                                <p>关键JavaScript文件加载失败，请刷新页面重试</p>
                                <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
                            </div>
                        `;
                    }
                }
            }, 100);
        });
    </script>

</body>
</html>
