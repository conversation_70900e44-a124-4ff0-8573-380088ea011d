/**
 * 学生控制器
 * 处理所有与学生相关的请求
 */

const db = require('../config/db');

/**
 * 获取当前用户可见的学生列表（带权限控制）
 * 管理员可以查看所有学生，教师只能查看自己任教的班级
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getAllStudents = async (req, res) => {
  try {
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    console.log(`用户 ${req.user.username} (${userRole}) 请求学生列表`);

    let students;

    if (userRole === 'admin') {
      // 管理员可以查看所有学生
      const { data, error } = await db.supabase
        .from('students')
        .select(`
          *,
          medals(count),
          typing_records(speed, accuracy)
        `)
        .order('grade')
        .order('class')
        .order('name');

      if (error) throw error;
      students = data;

    } else {
      // 普通教师只能查看自己任教的班级
      const { data, error } = await db.supabase
        .rpc('get_teacher_students', { p_teacher_id: userId });

      if (error) throw error;

      // 为每个学生获取奖章和打字记录
      const studentIds = data.map(s => s.student_identifier);

      if (studentIds.length > 0) {
        // 获取奖章数据
        const { data: medals, error: medalsError } = await db.supabase
          .from('medals')
          .select('student_identifier, count')
          .in('student_identifier', studentIds);

        if (medalsError) throw medalsError;

        // 获取打字记录
        const { data: typingRecords, error: typingError } = await db.supabase
          .from('typing_records')
          .select('student_identifier, speed, accuracy')
          .in('student_identifier', studentIds);

        if (typingError) throw typingError;

        // 合并数据
        students = data.map(student => ({
          ...student,
          medals: medals.filter(m => m.student_identifier === student.student_identifier),
          typing_records: typingRecords.filter(t => t.student_identifier === student.student_identifier)
        }));
      } else {
        students = [];
      }
    }

    // 处理数据，计算最佳成绩和奖章数量
    const processedStudents = students.map(student => ({
      ...student,
      medal_count: student.medals?.[0]?.count || 0,
      best_speed: student.typing_records?.length > 0
        ? Math.max(...student.typing_records.map(r => r.speed))
        : 0,
      best_accuracy: student.typing_records?.length > 0
        ? Math.max(...student.typing_records.map(r => r.accuracy))
        : 0
    }));

    console.log(`返回 ${processedStudents.length} 个学生记录给用户 ${req.user.username}`);

    res.status(200).json({
      data: processedStudents,
      total: processedStudents.length,
      user_role: userRole
    });
  } catch (error) {
    console.error('获取学生列表错误:', error);
    res.status(500).json({ error: '获取学生列表失败: ' + error.message });
  }
};

/**
 * 获取单个学生
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getStudent = async (req, res) => {
  try {
    const studentId = req.params.id;

    const { data: student, error } = await db.supabase
      .from('students')
      .select(`
        *,
        medals(count),
        typing_records(speed, accuracy)
      `)
      .eq('student_identifier', studentId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({ error: '学生不存在' });
      }
      throw error;
    }

    // 处理数据
    const processedStudent = {
      ...student,
      medal_count: student.medals?.[0]?.count || 0,
      best_speed: student.typing_records?.length > 0
        ? Math.max(...student.typing_records.map(r => r.speed))
        : 0,
      best_accuracy: student.typing_records?.length > 0
        ? Math.max(...student.typing_records.map(r => r.accuracy))
        : 0
    };

    res.status(200).json({ data: processedStudent });
  } catch (error) {
    console.error('获取学生信息错误:', error);
    res.status(500).json({ error: '获取学生信息失败: ' + error.message });
  }
};

/**
 * 创建学生
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.createStudent = async (req, res) => {
  // 验证必填字段
  const { name, grade, class: className } = req.body;

  if (!name || !grade || !className) {
    return res.status(400).json({ error: '姓名、年级和班级为必填项' });
  }

  try {
    // 构建学生标识符
    const identifier = `${grade}_${className}_${name}`;

    // 检查是否已存在
    const { data: existingStudent, error: checkError } = await db.supabase
      .from('students')
      .select('*')
      .eq('student_identifier', identifier)
      .single();

    if (existingStudent && !checkError) {
      return res.status(409).json({ error: '该学生已存在' });
    }

    // 插入新学生
    const { data: newStudent, error: insertError } = await db.supabase
      .from('students')
      .insert({
        student_identifier: identifier,
        name: name,
        grade: parseInt(grade),
        class: parseInt(className)
      })
      .select()
      .single();

    if (insertError) {
      throw insertError;
    }

    // 创建奖章记录
    const { error: medalError } = await db.supabase
      .from('medals')
      .insert({
        student_identifier: identifier,
        count: 0
      });

    if (medalError) {
      console.error('创建奖章记录失败:', medalError);
      // 不抛出错误，因为学生已创建成功
    }

    // 获取新创建的学生信息
    const { data: createdStudent, error: fetchError } = await db.supabase
      .from('students')
      .select(`
        *,
        medals(count),
        typing_records(speed, accuracy)
      `)
      .eq('student_identifier', identifier)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    // 处理数据
    const processedStudent = {
      ...createdStudent,
      medal_count: createdStudent.medals?.[0]?.count || 0,
      best_speed: 0,
      best_accuracy: 0
    };

    res.status(201).json({ data: processedStudent });
  } catch (error) {
    console.error('创建学生错误:', error);
    res.status(500).json({ error: '创建学生失败: ' + error.message });
  }
};

/**
 * 更新学生
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.updateStudent = async (req, res) => {
  try {
    const studentId = req.params.id;
    const { name, grade, class: className } = req.body;

    // 检查学生是否存在
    const { data: existingStudent, error: checkError } = await db.supabase
      .from('students')
      .select('*')
      .eq('student_identifier', studentId)
      .single();

    if (checkError || !existingStudent) {
      return res.status(404).json({ error: '学生不存在' });
    }

    // 构建更新对象
    const updateData = {};

    if (name) {
      updateData.name = name;
    }

    if (grade) {
      updateData.grade = parseInt(grade);
    }

    if (className) {
      updateData.class = parseInt(className);
    }

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({ error: '没有要更新的字段' });
    }

    // 执行更新
    const { data: updatedStudent, error: updateError } = await db.supabase
      .from('students')
      .update(updateData)
      .eq('student_identifier', studentId)
      .select()
      .single();

    if (updateError) {
      throw updateError;
    }

    // 获取更新后的学生信息
    const { data: studentWithDetails, error: fetchError } = await db.supabase
      .from('students')
      .select(`
        *,
        medals(count),
        typing_records(speed, accuracy)
      `)
      .eq('student_identifier', studentId)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    // 处理数据
    const processedStudent = {
      ...studentWithDetails,
      medal_count: studentWithDetails.medals?.[0]?.count || 0,
      best_speed: studentWithDetails.typing_records?.length > 0
        ? Math.max(...studentWithDetails.typing_records.map(r => r.speed))
        : 0,
      best_accuracy: studentWithDetails.typing_records?.length > 0
        ? Math.max(...studentWithDetails.typing_records.map(r => r.accuracy))
        : 0
    };

    res.status(200).json({ data: processedStudent });
  } catch (error) {
    console.error('更新学生信息错误:', error);
    res.status(500).json({ error: '更新学生信息失败: ' + error.message });
  }
};

/**
 * 删除学生
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.deleteStudent = async (req, res) => {
  try {
    const studentId = req.params.id;

    // 检查学生是否存在
    const { data: existingStudent, error: checkError } = await db.supabase
      .from('students')
      .select('*')
      .eq('student_identifier', studentId)
      .single();

    if (checkError || !existingStudent) {
      return res.status(404).json({ error: '学生不存在' });
    }

    // 删除学生（由于外键约束，相关数据会自动级联删除）
    const { error: deleteError } = await db.supabase
      .from('students')
      .delete()
      .eq('student_identifier', studentId);

    if (deleteError) {
      throw deleteError;
    }

    res.status(200).json({ data: { message: '学生删除成功' } });
  } catch (error) {
    console.error('删除学生错误:', error);
    res.status(500).json({ error: '删除学生失败: ' + error.message });
  }
};

/**
 * 获取教师任教的班级列表
 */
exports.getTeacherClasses = async (req, res) => {
  try {
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    console.log(`用户 ${req.user.username} 请求任教班级列表`);

    if (userRole === 'admin') {
      // 管理员可以查看所有班级
      const { data: allClasses, error } = await db.supabase
        .from('students')
        .select('grade, class')
        .order('grade, class');

      if (error) throw error;

      // 去重
      const uniqueClasses = allClasses.reduce((acc, curr) => {
        const key = `${curr.grade}-${curr.class}`;
        if (!acc.find(item => `${item.grade}-${item.class}` === key)) {
          acc.push(curr);
        }
        return acc;
      }, []);

      res.json({
        success: true,
        data: uniqueClasses,
        total: uniqueClasses.length,
        is_admin: true
      });
    } else {
      // 普通教师只能查看自己任教的班级
      const { data: teacherClasses, error } = await db.supabase
        .from('teacher_classes')
        .select('grade, class')
        .eq('teacher_id', userId)
        .order('grade, class');

      if (error) throw error;

      res.json({
        success: true,
        data: teacherClasses,
        total: teacherClasses.length,
        is_admin: false
      });
    }

  } catch (error) {
    console.error('获取任教班级错误:', error);
    res.status(500).json({
      success: false,
      error: '获取任教班级失败: ' + error.message
    });
  }
};

/**
 * 管理员分配教师到班级
 */
exports.assignTeacherToClass = async (req, res) => {
  try {
    // 只有管理员可以执行此操作
    if (req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: '只有管理员可以分配教师到班级'
      });
    }

    const { teacher_id, grade, class: classNum } = req.body;

    if (!teacher_id || !grade || !classNum) {
      return res.status(400).json({
        success: false,
        error: '教师ID、年级和班级都是必需的'
      });
    }

    // 检查教师是否存在
    const { data: teacher, error: teacherError } = await db.supabase
      .from('users')
      .select('username, role')
      .eq('id', teacher_id)
      .single();

    if (teacherError || !teacher) {
      return res.status(404).json({
        success: false,
        error: '教师不存在'
      });
    }

    // 分配教师到班级
    const { data: assignment, error } = await db.supabase
      .from('teacher_classes')
      .insert({
        teacher_id,
        grade: parseInt(grade),
        class: parseInt(classNum)
      })
      .select()
      .single();

    if (error) {
      if (error.code === '23505') { // 唯一约束违反
        return res.status(409).json({
          success: false,
          error: '该教师已经被分配到此班级'
        });
      }
      throw error;
    }

    res.json({
      success: true,
      message: `成功将教师 ${teacher.username} 分配到 ${grade}年级${classNum}班`,
      data: assignment
    });

  } catch (error) {
    console.error('分配教师到班级错误:', error);
    res.status(500).json({
      success: false,
      error: '分配教师到班级失败: ' + error.message
    });
  }
};