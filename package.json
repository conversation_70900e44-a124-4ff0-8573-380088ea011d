{"name": "class-management-system", "version": "1.0.0", "description": "班级成绩管理系统 - 基于Node.js + Express + Supabase PostgreSQL", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Build completed'", "vercel-build": "echo 'Vercel build completed'"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0"}, "devDependencies": {"@stagewise/toolbar": "^0.4.9", "nodemon": "^2.0.22"}, "engines": {"node": ">=18.0.0"}}