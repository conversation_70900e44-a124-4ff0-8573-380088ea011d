<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员控制台 - 班级成绩管理系统</title>

    <!-- 统一样式 -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        /* 管理界面专用样式 - 与主界面完全相同的背景 */
        body {
            background-color: #f0f4f8;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(74, 144, 226, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(103, 184, 255, 0.05) 0%, transparent 50%);
            color: #333;
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }

        /* 主容器样式 - 与主界面完全相同的毛玻璃效果 */
        .admin-container {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            max-width: 1600px;
            width: 95%;
            margin: 0 auto;
            overflow: hidden;
            display: flex;
            min-height: calc(100vh - 40px);
        }

        /* 侧边栏样式 */
        .sidebar {
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
            width: 280px;
            padding: 0;
            border-radius: 20px 0 0 20px;
        }

        .sidebar .nav-link {
            color: white;
            padding: 15px 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .sidebar .nav-link i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateX(5px);
            box-shadow: inset 4px 0 0 rgba(255, 255, 255, 0.5);
        }

        .content-area {
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 0 20px 20px 0;
        }

        .stats-card {
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card h3 {
            margin: 0;
            font-size: 2rem;
        }
        
        .stats-card p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        
        .management-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        
        .teacher-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .teacher-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .school-card {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .school-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        /* 确保模态框正确隐藏 */
        .modal {
            display: none !important;
        }

        .modal.show {
            display: block !important;
        }

        /* 自定义模态框样式以匹配主题 */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            border-bottom: none;
        }

        .modal-title {
            font-weight: 600;
        }

        .btn-close {
            filter: brightness(0) invert(1);
        }

        .modal-body {
            padding: 25px;
        }

        .modal-footer {
            border-top: 1px solid #e9ecef;
            padding: 20px 25px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #4a90e2;
            box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
        }

        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #357abd 0%, #4a90e2 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            border: none;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        /* 统计卡片样式 */
        .stats-card {
            background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
        }

        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 0;
            color: white;
        }

        .stats-card p {
            font-size: 1rem;
            margin: 5px 0 0 0;
            opacity: 0.9;
        }

        /* 管理界面表格样式 */
        .table-container {
            overflow-x: auto;
        }

        .table-container table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table-container th,
        .table-container td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table-container th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table-container tbody tr:hover {
            background: #f8f9fa;
        }

        /* 操作按钮样式 */
        .action-btn {
            padding: 5px 10px;
            margin: 0 2px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .action-btn.edit {
            background: #28a745;
            color: white;
        }

        .action-btn.delete {
            background: #dc3545;
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- 主要内容区域 -->
    <div class="admin-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 顶部用户信息 -->
            <div style="padding: 25px; border-bottom: 1px solid rgba(255,255,255,0.2); text-align: center;">
                <div style="color: white; font-size: 18px; font-weight: bold;">
                    <i class="fas fa-user-shield"></i> 管理员控制台
                </div>
                <div style="color: rgba(255,255,255,0.8); font-size: 14px; margin-top: 5px;" id="admin-user-info">
                    欢迎，管理员
                </div>
            </div>

            <!-- 导航菜单 -->
            <div class="nav flex-column">
                <a href="#" class="nav-link active" onclick="showAdminSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i> 控制台概览
                </a>
                <a href="#" class="nav-link" onclick="showAdminSection('teachers')">
                    <i class="fas fa-chalkboard-teacher"></i> 教师管理
                </a>
                <a href="#" class="nav-link" onclick="showAdminSection('schools')">
                    <i class="fas fa-school"></i> 学校信息
                    <span class="badge bg-secondary ms-1 small">只读</span>
                </a>
                <a href="#" class="nav-link" onclick="showAdminSection('students')">
                    <i class="fas fa-users"></i> 学生信息
                    <span class="badge bg-secondary ms-1 small">只读</span>
                </a>
            </div>

            <!-- 底部操作 -->
            <div style="position: absolute; bottom: 0; width: 100%; padding: 20px; border-top: 1px solid rgba(255,255,255,0.2);">
                <a href="/" class="nav-link" style="margin-bottom: 10px;">
                    <i class="fas fa-home"></i> 返回首页
                </a>
                <a href="#" class="nav-link" onclick="adminLogout()">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </a>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="content-area">
            <div id="admin-content-area">
                <!-- 动态内容将在这里加载 -->
                <div class="loading" style="text-align: center; padding: 50px; color: #666;">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p style="margin-top: 15px;">正在加载管理界面...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加教师模态框 -->
    <div class="modal fade" id="addTeacherModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加教师</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 字段说明 -->
                    <div class="alert alert-info mb-3">
                        <h6 class="mb-2"><i class="fas fa-info-circle"></i> 字段说明</h6>
                        <small>
                            <strong>用户名：</strong>教师登录系统时使用的唯一标识符，不可重复，用于身份验证<br>
                            <strong>显示名称：</strong>在系统界面中显示的教师姓名，用于用户友好的展示
                        </small>
                    </div>

                    <form id="addTeacherForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="teacherUsername" class="form-label">用户名 *</label>
                                    <input type="text" class="form-control" id="teacherUsername" required>
                                    <small class="form-text text-muted">用于登录系统的唯一标识符</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="teacherPassword" class="form-label">密码 *</label>
                                    <input type="password" class="form-control" id="teacherPassword" required>
                                    <small class="form-text text-muted">教师登录密码</small>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="teacherDisplayName" class="form-label">显示名称 *</label>
                            <input type="text" class="form-control" id="teacherDisplayName" required>
                            <small class="form-text text-muted">在系统中显示的教师姓名</small>
                        </div>
                        <!-- 任教学校选择 -->
                        <div class="mb-4">
                            <label for="teacherSchools" class="form-label fw-bold">
                                <i class="fas fa-school text-primary me-2"></i>任教学校
                            </label>
                            <select multiple class="form-control" id="teacherSchools" style="height: 120px;">
                                <option value="">加载中...</option>
                            </select>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                按住Ctrl键可选择多个学校，选择后将为每个学校单独配置年级班级
                            </small>
                        </div>

                        <!-- 分层级任教信息配置 -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-sitemap text-info me-2"></i>任教信息配置
                            </label>
                            <div id="teacherAssignmentContainer" class="border-0 rounded-3 p-4 shadow-sm"
                                 style="background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%); min-height: 150px;">
                                <div class="text-center text-muted py-5">
                                    <div class="mb-3">
                                        <i class="fas fa-arrow-up fa-3x text-primary opacity-50"></i>
                                    </div>
                                    <h6 class="mb-2">配置任教信息</h6>
                                    <p class="mb-0 small">请先在上方选择任教学校，系统将为每个学校生成独立的年级班级配置区域</p>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                每个学校可以独立配置不同的年级和班级，避免混淆
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmAddTeacher()">
                        <span class="spinner-border spinner-border-sm d-none" id="addTeacherSpinner"></span>
                        确认添加
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑教师模态框 -->
    <div class="modal fade" id="editTeacherModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑教师信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 字段说明 -->
                    <div class="alert alert-info mb-3">
                        <h6 class="mb-2"><i class="fas fa-info-circle"></i> 字段说明</h6>
                        <small>
                            <strong>用户名：</strong>教师登录系统时使用的唯一标识符，创建后不可修改<br>
                            <strong>显示名称：</strong>在系统界面中显示的教师姓名，可以修改
                        </small>
                    </div>

                    <form id="editTeacherForm">
                        <input type="hidden" id="editTeacherId">
                        <div class="mb-3">
                            <label for="editTeacherUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="editTeacherUsername" readonly>
                            <small class="form-text text-muted">用户名创建后不可修改</small>
                        </div>
                        <div class="mb-3">
                            <label for="editTeacherDisplayName" class="form-label">显示名称 *</label>
                            <input type="text" class="form-control" id="editTeacherDisplayName" required>
                            <small class="form-text text-muted">在系统中显示的教师姓名</small>
                        </div>
                        <!-- 任教学校选择 -->
                        <div class="mb-4">
                            <label for="editTeacherSchools" class="form-label fw-bold">
                                <i class="fas fa-school text-primary me-2"></i>任教学校
                            </label>
                            <select multiple class="form-control" id="editTeacherSchools" style="height: 120px;">
                                <option value="">加载中...</option>
                            </select>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                按住Ctrl键可选择多个学校，选择后将为每个学校单独配置年级班级
                            </small>
                        </div>

                        <!-- 分层级任教信息配置 -->
                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-sitemap text-info me-2"></i>任教信息配置
                            </label>
                            <div id="editTeacherAssignmentContainer" class="border-0 rounded-3 p-4 shadow-sm"
                                 style="background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%); min-height: 150px;">
                                <div class="text-center text-muted py-5">
                                    <div class="mb-3">
                                        <i class="fas fa-arrow-up fa-3x text-primary opacity-50"></i>
                                    </div>
                                    <h6 class="mb-2">配置任教信息</h6>
                                    <p class="mb-0 small">请先在上方选择任教学校，系统将为每个学校生成独立的年级班级配置区域</p>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                每个学校可以独立配置不同的年级和班级，避免混淆
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmEditTeacher()">
                        <span class="spinner-border spinner-border-sm d-none" id="editTeacherSpinner"></span>
                        保存更改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 重置教师密码模态框 -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">重置教师密码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="resetPasswordTeacherId">
                    <p>确定要为教师 <strong id="resetPasswordTeacherName"></strong> 重置密码吗？</p>
                    <p class="text-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        系统将生成一个新的临时密码，请将新密码告知该教师。
                    </p>
                    <div id="newPasswordDisplay" class="d-none">
                        <div class="alert alert-success">
                            <strong>新密码：</strong>
                            <span id="newPasswordText" class="font-monospace"></span>
                            <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="copyPassword()">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="confirmResetPassword()" id="resetPasswordBtn">
                        <span class="spinner-border spinner-border-sm d-none" id="resetPasswordSpinner"></span>
                        确认重置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑教师模态框 -->
    <div class="modal fade" id="editTeacherModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑教师</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editTeacherForm">
                        <input type="hidden" id="editTeacherId">
                        <div class="mb-3">
                            <label for="editTeacherUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="editTeacherUsername" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editTeacherDisplayName" class="form-label">显示名称 *</label>
                            <input type="text" class="form-control" id="editTeacherDisplayName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editTeacherNewPassword" class="form-label">新密码（留空则不修改）</label>
                            <input type="password" class="form-control" id="editTeacherNewPassword">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmEditTeacher()">确认修改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加学校模态框 -->
    <div class="modal fade" id="addSchoolModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加学校</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addSchoolForm">
                        <div class="mb-3">
                            <label for="schoolName" class="form-label">学校名称 *</label>
                            <input type="text" class="form-control" id="schoolName" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmAddSchool()">
                        <span class="spinner-border spinner-border-sm d-none" id="addSchoolSpinner"></span>
                        确认添加
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑学校模态框 -->
    <div class="modal fade" id="editSchoolModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑学校信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editSchoolForm">
                        <input type="hidden" id="editSchoolId">
                        <div class="mb-3">
                            <label for="editSchoolName" class="form-label">学校名称 *</label>
                            <input type="text" class="form-control" id="editSchoolName" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmEditSchool()">
                        <span class="spinner-border spinner-border-sm d-none" id="editSchoolSpinner"></span>
                        保存更改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加学生模态框 -->
    <div class="modal fade" id="addStudentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加学生</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addStudentForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="studentName" class="form-label">学生姓名 *</label>
                                    <input type="text" class="form-control" id="studentName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="studentIdentifier" class="form-label">学生标识符 *</label>
                                    <input type="text" class="form-control" id="studentIdentifier" required>
                                    <small class="form-text text-muted">格式：年级_班级_姓名，如：4_1_张三</small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="studentGrade" class="form-label">年级 *</label>
                                    <select class="form-control" id="studentGrade" required>
                                        <option value="">请选择年级</option>
                                        <option value="1">1年级</option>
                                        <option value="2">2年级</option>
                                        <option value="3">3年级</option>
                                        <option value="4">4年级</option>
                                        <option value="5">5年级</option>
                                        <option value="6">6年级</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="studentClass" class="form-label">班级 *</label>
                                    <select class="form-control" id="studentClass" required>
                                        <option value="">请选择班级</option>
                                        <option value="1">1班</option>
                                        <option value="2">2班</option>
                                        <option value="3">3班</option>
                                        <option value="4">4班</option>
                                        <option value="5">5班</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="studentSeat" class="form-label">座位号</label>
                                    <input type="number" class="form-control" id="studentSeat" min="1" max="50">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="studentSchool" class="form-label">所属学校</label>
                                    <select class="form-control" id="studentSchool">
                                        <option value="">请选择学校</option>
                                        <!-- 动态加载学校选项 -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="studentGender" class="form-label">性别</label>
                                    <select class="form-control" id="studentGender">
                                        <option value="">请选择性别</option>
                                        <option value="男">男</option>
                                        <option value="女">女</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmAddStudent()">
                        <span class="spinner-border spinner-border-sm d-none" id="addStudentSpinner"></span>
                        确认添加
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑学生模态框 -->
    <div class="modal fade" id="editStudentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑学生信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editStudentForm">
                        <input type="hidden" id="editStudentId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStudentName" class="form-label">学生姓名 *</label>
                                    <input type="text" class="form-control" id="editStudentName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStudentIdentifier" class="form-label">学生标识符</label>
                                    <input type="text" class="form-control" id="editStudentIdentifier" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editStudentGrade" class="form-label">年级 *</label>
                                    <select class="form-control" id="editStudentGrade" required>
                                        <option value="">请选择年级</option>
                                        <option value="1">1年级</option>
                                        <option value="2">2年级</option>
                                        <option value="3">3年级</option>
                                        <option value="4">4年级</option>
                                        <option value="5">5年级</option>
                                        <option value="6">6年级</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editStudentClass" class="form-label">班级 *</label>
                                    <select class="form-control" id="editStudentClass" required>
                                        <option value="">请选择班级</option>
                                        <option value="1">1班</option>
                                        <option value="2">2班</option>
                                        <option value="3">3班</option>
                                        <option value="4">4班</option>
                                        <option value="5">5班</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="editStudentSeat" class="form-label">座位号</label>
                                    <input type="number" class="form-control" id="editStudentSeat" min="1" max="50">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStudentSchool" class="form-label">所属学校</label>
                                    <select class="form-control" id="editStudentSchool">
                                        <option value="">请选择学校</option>
                                        <!-- 动态加载学校选项 -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStudentGender" class="form-label">性别</label>
                                    <select class="form-control" id="editStudentGender">
                                        <option value="">请选择性别</option>
                                        <option value="男">男</option>
                                        <option value="女">女</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">任课教师</label>
                            <div id="editStudentTeachers" class="form-control-plaintext">
                                <!-- 动态显示匹配的教师 -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmEditStudent()">
                        <span class="spinner-border spinner-border-sm d-none" id="editStudentSpinner"></span>
                        保存更改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 自定义JS -->
    <script src="js/config.js"></script>
    <script src="js/db.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/admin-management.js"></script>
    <script src="js/teacher-permission-management.js"></script>
    <script src="js/school-hierarchy-management.js"></script>
</body>
</html>
