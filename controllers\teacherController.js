/**
 * 教师权限管理控制器
 * 实现教师只能看到自己数据的权限控制
 */

const db = require('../config/db');

/**
 * 检查教师是否有访问指定学校的权限
 */
async function checkTeacherSchoolPermission(teacherId, schoolId) {
  const { data, error } = await db.supabase
    .from('teacher_school_assignments')
    .select('*')
    .eq('teacher_id', teacherId)
    .eq('school_id', schoolId)
    .single();
  
  return !error && data;
}

/**
 * 检查教师是否有访问指定班级的权限
 */
async function checkTeacherClassPermission(teacherId, schoolId, grade, classNum) {
  const { data, error } = await db.supabase
    .from('teacher_class_permissions')
    .select('*')
    .eq('teacher_id', teacherId)
    .eq('school_id', schoolId)
    .eq('grade', grade)
    .eq('class', classNum)
    .single();
  
  return !error && data;
}

/**
 * 获取教师可访问的所有学校
 */
async function getTeacherSchools(req, res) {
  try {
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    let query;
    if (isAdmin) {
      // 管理员可以看到所有学校
      query = db.supabase
        .from('schools')
        .select('*')
        .order('created_at', { ascending: false });
    } else {
      // 教师只能看到有权限的学校
      query = db.supabase
        .from('teacher_permissions_view')
        .select('school_id, school_name, teacher_id')
        .eq('teacher_id', teacherId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('获取教师学校数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取学校数据失败',
        error: error.message
      });
    }

    if (isAdmin) {
      res.json(data || []);
    } else {
      // 转换数据格式
      const schools = data ? data.map(item => ({
        id: item.school_id,
        name: item.school_name,
        created_at: new Date().toISOString()
      })) : [];
      
      // 去重
      const uniqueSchools = schools.filter((school, index, self) => 
        index === self.findIndex(s => s.id === school.id)
      );
      
      res.json(uniqueSchools);
    }
  } catch (error) {
    console.error('获取教师学校数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 获取教师可访问的所有学生
 */
async function getTeacherStudents(req, res) {
  try {
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (isAdmin) {
      // 管理员可以看到所有学生
      const { data, error } = await db.supabase
        .from('students')
        .select(`
          *,
          schools!inner(id, name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      res.json(data || []);
      return;
    }

    // 教师只能看到有权限的班级中的学生
    const { data, error } = await db.supabase
      .from('students')
      .select(`
        *,
        schools!inner(id, name)
      `)
      .in('school_id', 
        db.supabase
          .from('teacher_school_assignments')
          .select('school_id')
          .eq('teacher_id', teacherId)
      )
      .order('created_at', { ascending: false });

    if (error) {
      console.error('获取教师学生数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取学生数据失败',
        error: error.message
      });
    }

    res.json(data || []);
  } catch (error) {
    console.error('获取教师学生数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 获取教师可管理的班级
 */
async function getTeacherClasses(req, res) {
  try {
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (isAdmin) {
      // 管理员可以看到所有班级配置
      const { data, error } = await db.supabase
        .from('school_grade_configs')
        .select(`
          *,
          schools!inner(id, name)
        `)
        .order('grade', { ascending: true });

      if (error) throw error;
      res.json(data || []);
      return;
    }

    // 教师只能看到有权限的班级
    const { data, error } = await db.supabase
      .from('teacher_permissions_view')
      .select('*')
      .eq('teacher_id', teacherId)
      .not('grade', 'is', null)
      .not('class', 'is', null)
      .order('school_name, grade, class');

    if (error) {
      console.error('获取教师班级数据失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取班级数据失败',
        error: error.message
      });
    }

    res.json(data || []);
  } catch (error) {
    console.error('获取教师班级数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 添加学校（仅管理员或有权限的教师）
 */
async function addTeacherSchool(req, res) {
  try {
    const { name, address, phone } = req.body;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: '学校名称不能为空'
      });
    }

    // 检查学校名称是否已存在
    const { data: existingSchool } = await db.supabase
      .from('schools')
      .select('name')
      .eq('name', name.trim())
      .single();

    if (existingSchool) {
      return res.status(400).json({
        success: false,
        message: '学校名称已存在'
      });
    }

    // 创建学校
    console.log('准备创建学校:', {
      name: name.trim(),
      address: address?.trim() || null,
      contact_phone: phone?.trim() || null,
      teacherId,
      isAdmin
    });

    const { data: newSchool, error: schoolError } = await db.supabase
      .from('schools')
      .insert({
        name: name.trim(),
        address: address?.trim() || null,
        contact_phone: phone?.trim() || null,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (schoolError) {
      console.error('创建学校失败:', {
        error: schoolError,
        code: schoolError.code,
        message: schoolError.message,
        details: schoolError.details,
        hint: schoolError.hint
      });

      // 根据错误类型返回更具体的错误信息
      let errorMessage = '创建学校失败';
      if (schoolError.code === '23505') {
        errorMessage = '学校名称已存在';
      } else if (schoolError.code === '42501') {
        errorMessage = '权限不足，无法创建学校';
      } else if (schoolError.message) {
        errorMessage = schoolError.message;
      }

      return res.status(500).json({
        success: false,
        message: errorMessage,
        error: schoolError.message,
        code: schoolError.code
      });
    }

    console.log('学校创建成功:', newSchool);

    // 为创建学校的教师分配权限（如果不是管理员）
    if (!isAdmin) {
      console.log('为教师分配学校权限:', {
        teacherId,
        schoolId: newSchool.id
      });

      const { error: assignError } = await db.supabase
        .from('teacher_school_assignments')
        .insert({
          teacher_id: teacherId,
          school_id: newSchool.id,
          created_at: new Date().toISOString()
        });

      if (assignError) {
        console.error('分配学校权限失败:', {
          error: assignError,
          teacherId,
          schoolId: newSchool.id
        });

        // 权限分配失败，但学校已创建，返回警告而不是错误
        return res.status(201).json({
          success: true,
          message: '学校创建成功，但权限分配失败',
          data: newSchool,
          warning: '请联系管理员手动分配权限'
        });
      } else {
        console.log('学校权限分配成功');
      }
    }

    res.status(201).json({
      success: true,
      message: '学校创建成功',
      data: newSchool
    });
  } catch (error) {
    console.error('添加学校失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 更新学校信息
 */
async function updateTeacherSchool(req, res) {
  try {
    const schoolId = req.params.id;
    const { name, address, phone } = req.body;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    // 检查权限
    if (!isAdmin) {
      const hasPermission = await checkTeacherSchoolPermission(teacherId, schoolId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限修改此学校'
        });
      }
    }

    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (name) updateData.name = name.trim();
    if (address !== undefined) updateData.address = address?.trim() || null;
    if (phone !== undefined) updateData.contact_phone = phone?.trim() || null;

    const { data, error } = await db.supabase
      .from('schools')
      .update(updateData)
      .eq('id', schoolId)
      .select()
      .single();

    if (error) {
      console.error('更新学校失败:', error);
      return res.status(500).json({
        success: false,
        message: '更新学校失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: '学校信息更新成功',
      data
    });
  } catch (error) {
    console.error('更新学校失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 删除学校
 */
async function deleteTeacherSchool(req, res) {
  try {
    const schoolId = req.params.id;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    // 检查权限
    if (!isAdmin) {
      const hasPermission = await checkTeacherSchoolPermission(teacherId, schoolId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限删除此学校'
        });
      }
    }

    // 检查是否有学生关联到这个学校
    const { data: students } = await db.supabase
      .from('students')
      .select('id')
      .eq('school_id', schoolId)
      .limit(1);

    if (students && students.length > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除学校，还有学生关联到此学校'
      });
    }

    const { data, error } = await db.supabase
      .from('schools')
      .delete()
      .eq('id', schoolId)
      .select()
      .single();

    if (error) {
      console.error('删除学校失败:', error);
      return res.status(500).json({
        success: false,
        message: '删除学校失败',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: '学校删除成功',
      data
    });
  } catch (error) {
    console.error('删除学校失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 添加年级（在指定学校下）
 */
async function addGrade(req, res) {
  try {
    const { school_id, grade_number, class_count = 1 } = req.body;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (!school_id || !grade_number) {
      return res.status(400).json({
        success: false,
        message: '学校ID和年级不能为空'
      });
    }

    // 检查权限
    if (!isAdmin) {
      const hasPermission = await checkTeacherSchoolPermission(teacherId, school_id);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限在此学校添加年级'
        });
      }
    }

    const { data, error } = await db.supabase
      .from('school_grade_configs')
      .insert({
        school_id,
        grade: grade_number,
        class_count,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('添加年级失败:', error);
      return res.status(500).json({
        success: false,
        message: error.code === '23505' ? '该年级已存在' : '添加年级失败',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: '年级添加成功',
      data
    });
  } catch (error) {
    console.error('添加年级失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

/**
 * 添加班级权限（为教师分配特定班级的管理权限）
 */
async function addClassPermission(req, res) {
  try {
    const { school_id, grade, class: classNum } = req.body;
    const teacherId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (!school_id || !grade || !classNum) {
      return res.status(400).json({
        success: false,
        message: '学校ID、年级和班级不能为空'
      });
    }

    // 检查学校权限
    if (!isAdmin) {
      const hasSchoolPermission = await checkTeacherSchoolPermission(teacherId, school_id);
      if (!hasSchoolPermission) {
        return res.status(403).json({
          success: false,
          message: '您没有权限访问此学校'
        });
      }
    }

    const { data, error } = await db.supabase
      .from('teacher_class_permissions')
      .insert({
        teacher_id: teacherId,
        school_id,
        grade,
        class: classNum,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('添加班级权限失败:', error);
      return res.status(500).json({
        success: false,
        message: error.code === '23505' ? '班级权限已存在' : '添加班级权限失败',
        error: error.message
      });
    }

    res.status(201).json({
      success: true,
      message: '班级权限添加成功',
      data
    });
  } catch (error) {
    console.error('添加班级权限失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
}

module.exports = {
  getTeacherSchools,
  getTeacherStudents,
  getTeacherClasses,
  addTeacherSchool,
  updateTeacherSchool,
  deleteTeacherSchool,
  addGrade,
  addClassPermission,
  checkTeacherSchoolPermission,
  checkTeacherClassPermission
};